# Organization Permission Context Approach

## Overview

This document describes the new security approach that builds organization-specific permission contexts after successful authentication. This approach removes the "Enhanced" prefix and provides a clean, efficient way to manage permissions across organizational hierarchies.

## Key Concepts

### 1. Organization Permission Context

Each user has a permission context for every organization they can access:

```java
public class OrganizationPermissionContext {
    private UUID organizationId;
    private String organizationName;
    private Set<String> permissions;
    private Set<String> roles;
    private boolean isInherited; // true if inherited from parent organization
}
```

### 2. Security Context Building

After successful authentication, the system builds a complete security context:

```java
Map<UUID, OrganizationPermissionContext> contexts = 
    securityContextBuilder.buildOrganizationPermissionContexts(user);
```

## Architecture Components

### 1. SecurityContextBuilder

**Purpose**: Builds organization-specific permission contexts for a user

**Key Methods**:
- `buildOrganizationPermissionContexts(User user)` - Main method to build all contexts
- `buildDirectPermissionContext()` - Build context for direct role assignments
- `addInheritedPermissions()` - Add permissions inherited from parent organizations

**Process**:
1. Get all active user roles
2. Group roles by organization
3. Build direct permission contexts
4. Add inherited permissions from parent organizations
5. Return complete context map

### 2. AuthenticationContextService

**Purpose**: Manages authentication context lifecycle and provides permission checking utilities

**Key Methods**:
- `buildAuthenticationContext(User user)` - Build complete CustomAuthentication
- `refreshAuthenticationContext()` - Refresh context when permissions change
- `hasPermissionInAnyOrganization()` - Check permission across all organizations
- `hasPermissionInOrganizationHierarchy()` - Check permission with inheritance
- `getAccessSummary()` - Get user access summary

### 3. OrganizationPermissionEvaluator

**Purpose**: Spring Security PermissionEvaluator that uses organization contexts

**Key Features**:
- Uses pre-built permission contexts (no database queries during evaluation)
- Supports organization-specific permission checking
- Handles inheritance through organizational hierarchy
- Provides super admin bypass

### 4. SecurityContextUtils

**Purpose**: Utility class for easy permission checking in application code

**Key Methods**:
- `hasPermission(String permission)` - Check permission in any organization
- `hasPermissionInOrganization(UUID orgId, String permission)` - Organization-specific check
- `getAccessibleOrganizations()` - Get all accessible organizations
- `getOrganizationContext(UUID orgId)` - Get specific organization context

## Integration Points

### 1. UserService Integration

The `UserServiceImpl.getAuthenticationByKeycloakId()` method now uses the new approach:

```java
@Override
public CustomAuthentication getAuthenticationByKeycloakId(String keycloakId) {
    User user = userRepository.findOneByKeycloakId(keycloakId);
    // ... validation ...
    
    // Build complete authentication context with organization-specific permissions
    CustomAuthentication authentication = authenticationContextService.buildAuthenticationContext(user);
    
    return authentication;
}
```

### 2. CustomAuthentication Enhancement

Enhanced with organization permission contexts:

```java
public class CustomAuthentication implements Authentication {
    // Existing fields
    private User actor;
    private Set<String> roles;
    private Map<UUID, Set<String>> organizationToPermissionsMap;
    
    // New field
    private Map<UUID, OrganizationPermissionContext> organizationPermissionContexts;
    
    // New methods
    public boolean hasPermissionInOrganization(UUID organizationId, String permission);
    public boolean hasRoleInOrganization(UUID organizationId, String role);
    public Set<UUID> getAccessibleOrganizations();
    public OrganizationPermissionContext getOrganizationContext(UUID organizationId);
}
```

## Permission Inheritance Model

### 1. Direct Permissions
- User has direct role assignments in an organization
- All permissions from assigned roles are available
- Context marked as `isInherited = false`

### 2. Inherited Permissions
- User has role assignments in parent organizations
- Only inheritable permissions flow down to child organizations
- Context marked as `isInherited = true`

### 3. Inheritance Rules
- Roles must be marked as inheritable
- Permissions must have appropriate scope (global, organization, etc.)
- Inheritance follows organizational hierarchy (single-parent model)

## Usage Examples

### 1. Controller Permission Checking

```java
@RestController
public class UserController {
    
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #organizationId, 'user:create:organization')")
    public ResponseEntity<UserDTO> createUser(@PathVariable UUID organizationId, @RequestBody CreateUserRequest request) {
        // Implementation
    }
}
```

### 2. Service Layer Permission Checking

```java
@Service
public class UserService {
    
    @Autowired
    private SecurityContextUtils securityUtils;
    
    public List<User> getUsers(UUID organizationId) {
        if (!securityUtils.hasPermissionInOrganization(organizationId, "user:read:organization")) {
            throw new AccessDeniedException("Insufficient permissions");
        }
        // Implementation
    }
}
```

### 3. Programmatic Permission Checking

```java
// Check permission in any organization
if (securityUtils.hasPermission("user:create")) {
    // User can create users somewhere
}

// Check permission in specific organization
if (securityUtils.hasPermissionInOrganization(orgId, "user:create:organization")) {
    // User can create users in this organization
}

// Get all organizations where user can create users
Set<UUID> orgsWithCreatePermission = securityUtils.getOrganizationsWithPermission("user:create");
```

## Benefits

### 1. Performance
- **No Database Queries During Permission Evaluation**: All permissions are pre-loaded
- **Fast Context Lookups**: In-memory permission checking
- **Reduced Query Load**: Single context build per authentication

### 2. Simplicity
- **No "Enhanced" Prefix**: Clean, simple naming
- **Clear Inheritance Model**: Follows organizational hierarchy only
- **Easy Integration**: Works with existing Spring Security annotations

### 3. Flexibility
- **Organization-Specific Contexts**: Clear separation of permissions by organization
- **Inheritance Support**: Automatic permission inheritance from parent organizations
- **Easy Extension**: Simple to add new permission types or scopes

### 4. Maintainability
- **Single Source of Truth**: Permission contexts built once per authentication
- **Clear Separation**: Security logic separated from business logic
- **Easy Testing**: Contexts can be easily mocked for testing

## Migration from Enhanced Approach

### 1. Entity Changes
- Remove "Enhanced" prefix from all entities
- Use existing Role, Permission, UserRole entities
- Add organization reference to roles if needed

### 2. Service Changes
- Replace EnhancedRoleManagementService with standard role services
- Use SecurityContextBuilder instead of complex hierarchy services
- Simplify permission evaluation logic

### 3. API Changes
- Update @PreAuthorize annotations to use new permission evaluator
- Replace complex permission strings with simple resource:action format
- Use SecurityContextUtils for programmatic checking

## Configuration

### 1. Spring Security Configuration

```java
@Configuration
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityContextConfiguration {
    
    @Bean
    public MethodSecurityExpressionHandler methodSecurityExpressionHandler() {
        DefaultMethodSecurityExpressionHandler expressionHandler = new DefaultMethodSecurityExpressionHandler();
        expressionHandler.setPermissionEvaluator(organizationPermissionEvaluator);
        return expressionHandler;
    }
}
```

### 2. Permission Format

Standard format: `resource:subResource:action`

Examples:
- `user:profile:read`
- `organization:settings:update`
- `report:financial:create`

## Monitoring and Debugging

### 1. Access Summary
```java
Map<String, Object> summary = securityUtils.getAccessSummary();
// Returns: totalOrganizations, directAccess, inheritedAccess, totalPermissions, etc.
```

### 2. Context Validation
```java
boolean isValid = securityUtils.validateCurrentContext();
// Validates authentication context integrity
```

### 3. Context Refresh
```java
boolean refreshed = securityUtils.refreshAuthenticationContext();
// Refreshes context when permissions change
```

This approach provides a clean, efficient, and maintainable security system that builds organization-specific permission contexts after authentication, eliminating the need for complex database queries during permission evaluation while maintaining clear organizational boundaries and inheritance rules.
