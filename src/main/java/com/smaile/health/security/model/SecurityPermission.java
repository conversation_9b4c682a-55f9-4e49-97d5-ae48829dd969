package com.smaile.health.security.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * Represents a structured security permission with resource, action, and scope.
 * This is an immutable value object that encapsulates permission logic.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SecurityPermission {
    private SecurityResource resource;
    private SecurityAction action;
    private SecurityScope scope;
    private String condition; // Optional condition for dynamic permissions

    /**
     * Create a permission from string format: resource:action:scope[:condition]
     */
    public static SecurityPermission fromString(String permissionString) {
        if (permissionString == null || permissionString.trim().isEmpty()) {
            throw new IllegalArgumentException("Permission string cannot be null or empty");
        }

        String[] parts = permissionString.split(":");
        if (parts.length < 3 || parts.length > 4) {
            throw new IllegalArgumentException("Invalid permission format. Expected: resource:action:scope[:condition]");
        }

        SecurityResource resource = SecurityResource.fromCode(parts[0]);
        SecurityAction action = SecurityAction.fromCode(parts[1]);
        SecurityScope scope = SecurityScope.fromCode(parts[2]);
        String condition = parts.length == 4 ? parts[3] : null;

        return SecurityPermission.builder()
                .resource(resource)
                .action(action)
                .scope(scope)
                .condition(condition)
                .build();
    }

    /**
     * Convert permission to string format
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(resource.getCode())
          .append(":")
          .append(action.getCode())
          .append(":")
          .append(scope.getCode());
        
        if (condition != null && !condition.trim().isEmpty()) {
            sb.append(":").append(condition);
        }
        
        return sb.toString();
    }

    /**
     * Check if this permission implies another permission
     */
    public boolean implies(SecurityPermission other) {
        if (other == null) {
            return false;
        }

        // Resource must match exactly or be a wildcard
        if (!this.resource.equals(other.resource)) {
            return false;
        }

        // Action must match exactly or this must be MANAGE (which implies all actions)
        if (!this.action.equals(other.action) && this.action != SecurityAction.MANAGE) {
            return false;
        }

        // Scope must include the other scope
        if (!this.scope.includes(other.scope)) {
            return false;
        }

        // If both have conditions, they must match exactly
        // If this has no condition but other does, this implies other
        // If this has condition but other doesn't, this doesn't imply other
        if (this.condition != null && other.condition != null) {
            return this.condition.equals(other.condition);
        } else if (this.condition != null && other.condition == null) {
            return false;
        }

        return true;
    }

    /**
     * Create a wildcard permission for a resource
     */
    public static SecurityPermission wildcard(SecurityResource resource, SecurityScope scope) {
        return SecurityPermission.builder()
                .resource(resource)
                .action(SecurityAction.MANAGE)
                .scope(scope)
                .build();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SecurityPermission that = (SecurityPermission) o;
        return resource == that.resource &&
               action == that.action &&
               scope == that.scope &&
               Objects.equals(condition, that.condition);
    }

    @Override
    public int hashCode() {
        return Objects.hash(resource, action, scope, condition);
    }
}
