package com.smaile.health.security.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration of security scopes that define the organizational boundary of permissions.
 * Scopes determine the hierarchical level at which permissions apply.
 */
@Getter
@AllArgsConstructor
public enum SecurityScope {
    // Organizational Scopes
    GLOBAL("global", "System-wide access across all organizations"),
    ORGANIZATION("organization", "Access within user's organization only"),
    CHILD_ORGANIZATIONS("child_organizations", "Access to child organizations only"),
    ORGANIZATION_TREE("organization_tree", "Access to organization and all descendants"),
    
    // Functional Scopes
    SELF("self", "Access to own data only"),
    TEAM("team", "Access to team/department data"),
    <PERSON><PERSON><PERSON>("branch", "Access to branch/location data"),
    
    // Special Scopes
    NONE("none", "No organizational scope - applies to system-level operations"),
    CUSTOM("custom", "Custom scope defined by business rules");

    private final String code;
    private final String description;

    /**
     * Get scope by code
     */
    public static SecurityScope fromCode(String code) {
        for (SecurityScope scope : values()) {
            if (scope.code.equals(code)) {
                return scope;
            }
        }
        throw new IllegalArgumentException("Unknown security scope code: " + code);
    }

    /**
     * Check if this scope includes another scope
     */
    public boolean includes(SecurityScope other) {
        return switch (this) {
            case GLOBAL -> true; // Global includes everything
            case ORGANIZATION_TREE -> other == ORGANIZATION || other == CHILD_ORGANIZATIONS || other == ORGANIZATION_TREE;
            case ORGANIZATION -> other == ORGANIZATION || other == SELF;
            case CHILD_ORGANIZATIONS -> other == CHILD_ORGANIZATIONS;
            case TEAM -> other == TEAM || other == SELF;
            case BRANCH -> other == BRANCH || other == TEAM || other == SELF;
            case SELF -> other == SELF;
            case NONE, CUSTOM -> other == this;
        };
    }
}
