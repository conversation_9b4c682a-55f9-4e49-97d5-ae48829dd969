package com.smaile.health.security.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enumeration of all security actions that can be performed on resources.
 * Actions follow CRUD+ pattern with additional business-specific operations.
 */
@Getter
@AllArgsConstructor
public enum SecurityAction {
    // Basic CRUD Operations
    CREATE("create", "Create new entities"),
    READ("read", "Read/view entities"),
    UPDATE("update", "Update existing entities"),
    DELETE("delete", "Delete entities"),
    
    // Extended Operations
    LIST("list", "List/search entities"),
    EXPORT("export", "Export data"),
    IMPORT("import", "Import data"),
    
    // Administrative Operations
    MANAGE("manage", "Full management access"),
    ASSIGN("assign", "Assign roles or permissions"),
    REVOKE("revoke", "Revoke roles or permissions"),
    
    // Approval Workflow
    APPROVE("approve", "Approve requests or changes"),
    REJECT("reject", "Reject requests or changes"),
    REVIEW("review", "Review for approval"),
    
    // Audit and Monitoring
    AUDIT("audit", "Access audit logs"),
    MONITOR("monitor", "Monitor system activities"),
    
    // Special Operations
    ARCHIVE("archive", "Archive entities"),
    RESTORE("restore", "Restore archived entities"),
    EXECUTE("execute", "Execute operations or reports");

    private final String code;
    private final String description;

    /**
     * Get action by code
     */
    public static SecurityAction fromCode(String code) {
        for (SecurityAction action : values()) {
            if (action.code.equals(code)) {
                return action;
            }
        }
        throw new IllegalArgumentException("Unknown security action code: " + code);
    }
}
