package com.smaile.health.security.service;

import com.smaile.health.config.CustomAuthentication;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
// Audit imports removed as per requirements
import com.smaile.health.security.domain.EnhancedPermission;
import com.smaile.health.security.model.SecurityPermission;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * Enhanced permission evaluator with fine-grained permission checking.
 * Supports hierarchical permissions, organizational scope, and comprehensive audit logging.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class EnhancedPermissionEvaluator implements PermissionEvaluator {

    private final RoleHierarchyService roleHierarchyService;
    private final OrganizationHierarchyService organizationHierarchyService;
    // Audit service removed as per requirements

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        if (!(authentication instanceof CustomAuthentication customAuth)) {
            log.warn("Authentication is not CustomAuthentication type");
            return false;
        }

        try {
            return evaluatePermission(customAuth, targetDomainObject, permission);
        } catch (Exception e) {
            log.error("Error evaluating permission: {}", e.getMessage(), e);
            // Audit logging removed as per requirements
            return false;
        }
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        if (!(authentication instanceof CustomAuthentication customAuth)) {
            log.warn("Authentication is not CustomAuthentication type");
            return false;
        }

        try {
            return evaluatePermissionById(customAuth, targetId, targetType, permission);
        } catch (Exception e) {
            log.error("Error evaluating permission by ID: {}", e.getMessage(), e);
            // Audit logging removed as per requirements
            return false;
        }
    }

    private boolean evaluatePermission(CustomAuthentication authentication, Object targetDomainObject, Object permission) {
        User user = authentication.getActor();
        
        // Super admin has access to everything
        if (isSuperAdmin(authentication)) {
            logPermissionCheck(user, permission.toString(), true, "Super admin access");
            return true;
        }

        // Parse permission string
        SecurityPermission securityPermission;
        try {
            securityPermission = SecurityPermission.fromString(permission.toString());
        } catch (Exception e) {
            log.warn("Invalid permission format: {}", permission);
            logPermissionCheck(user, permission.toString(), false, "Invalid permission format");
            return false;
        }

        // Extract organization ID from target domain object
        UUID organizationId = extractOrganizationId(targetDomainObject);
        if (organizationId == null && requiresOrganizationContext(securityPermission)) {
            log.warn("Missing organization context for permission: {}", permission);
            logPermissionCheck(user, permission.toString(), false, "Missing organization context");
            return false;
        }

        // Get user's effective permissions
        Set<EnhancedPermission> userPermissions = roleHierarchyService.getEffectivePermissions(user);
        
        // Check if user has the required permission
        boolean hasPermission = userPermissions.stream()
            .anyMatch(p -> permissionImplies(p, securityPermission, organizationId, user));

        // If not found in direct permissions, check inherited permissions from parent organizations
        if (!hasPermission && organizationId != null) {
            hasPermission = checkInheritedPermissionsFromOrganizations(user, securityPermission, organizationId);
        }

        String reason = hasPermission ? "Permission granted" : "Permission denied";
        logPermissionCheck(user, permission.toString(), hasPermission, reason);

        return hasPermission;
    }

    private boolean evaluatePermissionById(CustomAuthentication authentication, Serializable targetId, 
                                         String targetType, Object permission) {
        // Convert targetId to organization ID if it's an organization-related resource
        UUID organizationId = null;
        if (targetId != null) {
            try {
                organizationId = UUID.fromString(targetId.toString());
            } catch (IllegalArgumentException e) {
                log.warn("Invalid target ID format: {}", targetId);
            }
        }

        return evaluatePermission(authentication, organizationId, permission);
    }

    private boolean permissionImplies(EnhancedPermission userPermission, SecurityPermission requiredPermission, 
                                    UUID organizationId, User user) {
        // Check basic permission implication
        SecurityPermission userSecurityPermission = SecurityPermission.builder()
            .resource(userPermission.getResource())
            .action(userPermission.getAction())
            .scope(userPermission.getScope())
            .condition(userPermission.getCondition())
            .build();

        if (!userSecurityPermission.implies(requiredPermission)) {
            return false;
        }

        // Check organizational scope
        return checkOrganizationalScope(userPermission, organizationId, user);
    }

    private boolean checkOrganizationalScope(EnhancedPermission permission, UUID organizationId, User user) {
        if (organizationId == null) {
            return true; // No organizational constraint
        }

        Organization userOrganization = user.getOrganization();
        if (userOrganization == null) {
            return false;
        }

        return switch (permission.getScope()) {
            case GLOBAL -> true; // Global scope allows access to any organization
            
            case ORGANIZATION -> userOrganization.getId().equals(organizationId);
            
            case CHILD_ORGANIZATIONS -> organizationHierarchyService.isDescendant(
                getOrganizationById(organizationId), userOrganization);
            
            case ORGANIZATION_TREE -> userOrganization.getId().equals(organizationId) ||
                organizationHierarchyService.isDescendant(getOrganizationById(organizationId), userOrganization);
            
            case SELF -> userOrganization.getId().equals(organizationId) && 
                isUserResource(permission.getResource());
            
            case TEAM, BRANCH -> checkTeamOrBranchScope(permission, organizationId, user);
            
            case NONE -> true; // No organizational scope constraint
            
            case CUSTOM -> evaluateCustomScope(permission, organizationId, user);
        };
    }

    private boolean checkInheritedPermissionsFromOrganizations(User user, SecurityPermission requiredPermission, UUID organizationId) {
        Organization targetOrganization = getOrganizationById(organizationId);
        if (targetOrganization == null) {
            return false;
        }

        // Check permissions inherited from parent organizations only
        Set<EnhancedPermission> inheritedPermissions = roleHierarchyService.getInheritedPermissionsFromOrganizations(
            user, targetOrganization);

        return inheritedPermissions.stream()
            .anyMatch(p -> {
                SecurityPermission inheritedSecurityPermission = SecurityPermission.builder()
                    .resource(p.getResource())
                    .action(p.getAction())
                    .scope(p.getScope())
                    .condition(p.getCondition())
                    .build();
                return inheritedSecurityPermission.implies(requiredPermission);
            });
    }

    private boolean isSuperAdmin(CustomAuthentication authentication) {
        return authentication.getRoles().contains(RoleEnum.SUPER_SMAILE_ADMIN.name());
    }

    private UUID extractOrganizationId(Object targetDomainObject) {
        if (targetDomainObject == null) {
            return null;
        }

        if (targetDomainObject instanceof UUID) {
            return (UUID) targetDomainObject;
        }

        if (targetDomainObject instanceof String) {
            try {
                return UUID.fromString((String) targetDomainObject);
            } catch (IllegalArgumentException e) {
                log.debug("Target domain object is not a valid UUID: {}", targetDomainObject);
            }
        }

        // Try to extract organization ID from domain objects
        if (targetDomainObject instanceof Organization org) {
            return org.getId();
        }

        // Add more domain object types as needed
        return null;
    }

    private boolean requiresOrganizationContext(SecurityPermission permission) {
        // Permissions with organizational scope require organization context
        return switch (permission.getScope()) {
            case ORGANIZATION, CHILD_ORGANIZATIONS, ORGANIZATION_TREE, SELF, TEAM, BRANCH -> true;
            case GLOBAL, NONE, CUSTOM -> false;
        };
    }

    private boolean checkTeamOrBranchScope(EnhancedPermission permission, UUID organizationId, User user) {
        // This would implement team/branch-specific logic
        // For now, treat as organization scope
        return user.getOrganization().getId().equals(organizationId);
    }

    private boolean evaluateCustomScope(EnhancedPermission permission, UUID organizationId, User user) {
        // This would implement custom scope evaluation based on the condition
        // For now, return false for safety
        log.warn("Custom scope evaluation not implemented for permission: {}", permission.getPermissionString());
        return false;
    }

    private boolean isUserResource(com.smaile.health.security.model.SecurityResource resource) {
        return resource == com.smaile.health.security.model.SecurityResource.USER;
    }

    private Organization getOrganizationById(UUID organizationId) {
        // This would be injected OrganizationService
        // For now, return null - this needs to be implemented
        return null;
    }

    private void logPermissionCheck(User user, String permission, boolean granted, String reason) {
        // Audit logging removed as per requirements
        log.debug("Permission check: {} for user {} - {} ({})",
            permission, user.getEmail(), granted ? "GRANTED" : "DENIED", reason);
    }
}
