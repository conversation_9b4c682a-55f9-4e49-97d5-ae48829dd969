package com.smaile.health.security.service;

import com.smaile.health.domain.User;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.security.audit.SecurityAuditService;
import com.smaile.health.security.audit.SecurityEventType;
import com.smaile.health.security.domain.EnhancedPermission;
import com.smaile.health.security.model.SecurityAction;
import com.smaile.health.security.model.SecurityPermission;
import com.smaile.health.security.model.SecurityResource;
import com.smaile.health.security.model.SecurityScope;
import com.smaile.health.security.repository.EnhancedPermissionRepository;
import com.smaile.health.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for managing enhanced permissions.
 * Provides methods to create, update, delete, and query permissions with enterprise-level validation.
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class EnhancedPermissionService {

    private final EnhancedPermissionRepository permissionRepository;
    private final SecurityAuditService auditService;

    /**
     * Create a new permission
     */
    public EnhancedPermission createPermission(SecurityResource resource, SecurityAction action, 
                                             SecurityScope scope, String condition, String description) {
        validatePermissionCreation(resource, action, scope, condition);
        
        // Check if permission already exists
        Optional<EnhancedPermission> existing = condition != null ?
            permissionRepository.findByResourceAndActionAndScopeAndCondition(resource, action, scope, condition) :
            permissionRepository.findByResourceAndActionAndScopeAndConditionIsNull(resource, action, scope);
        
        if (existing.isPresent()) {
            throw new SmaileRuntimeException("Permission already exists: " + 
                SecurityPermission.builder()
                    .resource(resource)
                    .action(action)
                    .scope(scope)
                    .condition(condition)
                    .build().toString());
        }
        
        EnhancedPermission permission = EnhancedPermission.builder()
                .id(UUID.randomUUID())
                .resource(resource)
                .action(action)
                .scope(scope)
                .condition(condition)
                .description(description)
                .systemPermission(false)
                .status("ACTIVE")
                .build();
        
        EnhancedPermission savedPermission = permissionRepository.save(permission);
        
        auditService.logSecurityEvent(
            SecurityEventType.PERMISSION_CREATED,
            "Permission created: " + savedPermission.getPermissionString(),
            "PERMISSION", savedPermission.getId(), savedPermission.getName()
        );
        
        log.info("Permission created: {}", savedPermission.getPermissionString());
        return savedPermission;
    }

    /**
     * Create permission from string format
     */
    public EnhancedPermission createPermission(String permissionString, String description) {
        SecurityPermission securityPermission = SecurityPermission.fromString(permissionString);
        return createPermission(
            securityPermission.getResource(),
            securityPermission.getAction(),
            securityPermission.getScope(),
            securityPermission.getCondition(),
            description
        );
    }

    /**
     * Update permission description
     */
    public EnhancedPermission updatePermission(UUID permissionId, String description) {
        EnhancedPermission permission = getPermissionById(permissionId);
        
        String oldDescription = permission.getDescription();
        permission.setDescription(description);
        
        EnhancedPermission updatedPermission = permissionRepository.save(permission);
        
        Map<String, Object> details = Map.of(
            "oldDescription", oldDescription != null ? oldDescription : "",
            "newDescription", description != null ? description : ""
        );
        
        auditService.logSecurityEvent(
            SecurityEventType.PERMISSION_UPDATED,
            "Permission updated: " + permission.getPermissionString(),
            "PERMISSION", permission.getId(), permission.getName(),
            details, true, null
        );
        
        log.info("Permission updated: {}", permission.getPermissionString());
        return updatedPermission;
    }

    /**
     * Delete permission (soft delete by setting status to INACTIVE)
     */
    public void deletePermission(UUID permissionId) {
        EnhancedPermission permission = getPermissionById(permissionId);
        
        if (permission.isSystemPermission()) {
            throw new SmaileRuntimeException("Cannot delete system permission: " + permission.getPermissionString());
        }
        
        // Check if permission is assigned to any roles
        if (!permission.getRoles().isEmpty()) {
            throw new SmaileRuntimeException("Cannot delete permission that is assigned to roles: " + 
                permission.getPermissionString());
        }
        
        permission.setStatus("INACTIVE");
        permissionRepository.save(permission);
        
        auditService.logSecurityEvent(
            SecurityEventType.PERMISSION_DELETED,
            "Permission deleted: " + permission.getPermissionString(),
            "PERMISSION", permission.getId(), permission.getName()
        );
        
        log.info("Permission deleted: {}", permission.getPermissionString());
    }

    /**
     * Get permission by ID
     */
    @Transactional(readOnly = true)
    public EnhancedPermission getPermissionById(UUID permissionId) {
        return permissionRepository.findById(permissionId)
            .orElseThrow(() -> new SmaileRuntimeException("Permission not found: " + permissionId));
    }

    /**
     * Get permission by string format
     */
    @Transactional(readOnly = true)
    public Optional<EnhancedPermission> getPermissionByString(String permissionString) {
        SecurityPermission securityPermission = SecurityPermission.fromString(permissionString);
        
        return securityPermission.getCondition() != null ?
            permissionRepository.findByResourceAndActionAndScopeAndCondition(
                securityPermission.getResource(),
                securityPermission.getAction(),
                securityPermission.getScope(),
                securityPermission.getCondition()) :
            permissionRepository.findByResourceAndActionAndScopeAndConditionIsNull(
                securityPermission.getResource(),
                securityPermission.getAction(),
                securityPermission.getScope());
    }

    /**
     * Get all active permissions
     */
    @Transactional(readOnly = true)
    public List<EnhancedPermission> getAllActivePermissions() {
        return permissionRepository.findAll().stream()
            .filter(p -> "ACTIVE".equals(p.getStatus()))
            .collect(Collectors.toList());
    }

    /**
     * Get permissions by resource
     */
    @Transactional(readOnly = true)
    public List<EnhancedPermission> getPermissionsByResource(SecurityResource resource) {
        return permissionRepository.findByResourceAndStatus(resource, "ACTIVE");
    }

    /**
     * Get permissions by action
     */
    @Transactional(readOnly = true)
    public List<EnhancedPermission> getPermissionsByAction(SecurityAction action) {
        return permissionRepository.findByActionAndStatus(action, "ACTIVE");
    }

    /**
     * Get permissions by scope
     */
    @Transactional(readOnly = true)
    public List<EnhancedPermission> getPermissionsByScope(SecurityScope scope) {
        return permissionRepository.findByScopeAndStatus(scope, "ACTIVE");
    }

    /**
     * Search permissions by name
     */
    @Transactional(readOnly = true)
    public Page<EnhancedPermission> searchPermissions(String namePattern, Pageable pageable) {
        return permissionRepository.findByNameContainingIgnoreCase(namePattern, "ACTIVE", pageable);
    }

    /**
     * Get permissions that imply a specific permission
     */
    @Transactional(readOnly = true)
    public List<EnhancedPermission> getPermissionsThatImply(SecurityResource resource, SecurityAction action) {
        return permissionRepository.findPermissionsThatImply(resource, action, "ACTIVE");
    }

    /**
     * Get inheritable permissions (can be inherited by child organizations)
     */
    @Transactional(readOnly = true)
    public List<EnhancedPermission> getInheritablePermissions() {
        return permissionRepository.findInheritablePermissions("ACTIVE");
    }

    /**
     * Get unassigned permissions (not assigned to any role)
     */
    @Transactional(readOnly = true)
    public List<EnhancedPermission> getUnassignedPermissions() {
        return permissionRepository.findUnassignedPermissions("ACTIVE");
    }

    /**
     * Check if a permission exists
     */
    @Transactional(readOnly = true)
    public boolean permissionExists(SecurityResource resource, SecurityAction action, 
                                   SecurityScope scope, String condition) {
        return permissionRepository.existsByPermissionString(resource, action, scope, condition, "ACTIVE");
    }

    /**
     * Get permission statistics
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getPermissionStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // Total permissions
        long totalPermissions = permissionRepository.countByStatus("ACTIVE");
        stats.put("totalPermissions", totalPermissions);
        
        // System permissions
        long systemPermissions = permissionRepository.findBySystemPermissionTrueAndStatus("ACTIVE").size();
        stats.put("systemPermissions", systemPermissions);
        
        // Permissions with conditions
        long conditionalPermissions = permissionRepository.findByConditionIsNotNullAndStatus("ACTIVE").size();
        stats.put("conditionalPermissions", conditionalPermissions);
        
        // Unassigned permissions
        long unassignedPermissions = permissionRepository.findUnassignedPermissions("ACTIVE").size();
        stats.put("unassignedPermissions", unassignedPermissions);
        
        // Statistics by resource
        List<Object[]> resourceStats = permissionRepository.getPermissionStatisticsByResource("ACTIVE");
        Map<String, Map<String, Object>> resourceStatsMap = new HashMap<>();
        for (Object[] stat : resourceStats) {
            Map<String, Object> resourceStat = new HashMap<>();
            resourceStat.put("permissionCount", stat[1]);
            resourceStat.put("actionCount", stat[2]);
            resourceStatsMap.put(stat[0].toString(), resourceStat);
        }
        stats.put("resourceStatistics", resourceStatsMap);
        
        // Statistics by action
        List<Object[]> actionStats = permissionRepository.getPermissionStatisticsByAction("ACTIVE");
        Map<String, Map<String, Object>> actionStatsMap = new HashMap<>();
        for (Object[] stat : actionStats) {
            Map<String, Object> actionStat = new HashMap<>();
            actionStat.put("permissionCount", stat[1]);
            actionStat.put("resourceCount", stat[2]);
            actionStatsMap.put(stat[0].toString(), actionStat);
        }
        stats.put("actionStatistics", actionStatsMap);
        
        // Statistics by scope
        List<Object[]> scopeStats = permissionRepository.getPermissionStatisticsByScope("ACTIVE");
        Map<String, Map<String, Object>> scopeStatsMap = new HashMap<>();
        for (Object[] stat : scopeStats) {
            Map<String, Object> scopeStat = new HashMap<>();
            scopeStat.put("permissionCount", stat[1]);
            scopeStat.put("resourceCount", stat[2]);
            scopeStatsMap.put(stat[0].toString(), scopeStat);
        }
        stats.put("scopeStatistics", scopeStatsMap);
        
        return stats;
    }

    /**
     * Create standard permissions for a resource
     */
    public List<EnhancedPermission> createStandardPermissions(SecurityResource resource, SecurityScope scope) {
        List<EnhancedPermission> permissions = new ArrayList<>();
        
        // Create basic CRUD permissions
        SecurityAction[] basicActions = {
            SecurityAction.CREATE, SecurityAction.READ, SecurityAction.UPDATE, SecurityAction.DELETE, SecurityAction.LIST
        };
        
        for (SecurityAction action : basicActions) {
            if (!permissionExists(resource, action, scope, null)) {
                EnhancedPermission permission = createPermission(resource, action, scope, null,
                    String.format("Standard %s permission for %s with %s scope", 
                        action.getDescription(), resource.getDescription(), scope.getDescription()));
                permissions.add(permission);
            }
        }
        
        // Create management permission
        if (!permissionExists(resource, SecurityAction.MANAGE, scope, null)) {
            EnhancedPermission managePermission = createPermission(resource, SecurityAction.MANAGE, scope, null,
                String.format("Full management permission for %s with %s scope", 
                    resource.getDescription(), scope.getDescription()));
            permissions.add(managePermission);
        }
        
        log.info("Created {} standard permissions for resource {} with scope {}", 
                permissions.size(), resource, scope);
        
        return permissions;
    }

    private void validatePermissionCreation(SecurityResource resource, SecurityAction action, 
                                          SecurityScope scope, String condition) {
        if (resource == null) {
            throw new IllegalArgumentException("Resource cannot be null");
        }
        if (action == null) {
            throw new IllegalArgumentException("Action cannot be null");
        }
        if (scope == null) {
            throw new IllegalArgumentException("Scope cannot be null");
        }
        
        // Validate condition format if present
        if (condition != null && condition.trim().isEmpty()) {
            throw new IllegalArgumentException("Condition cannot be empty string");
        }
        
        // Business rule validations
        validateBusinessRules(resource, action, scope, condition);
    }

    private void validateBusinessRules(SecurityResource resource, SecurityAction action, 
                                     SecurityScope scope, String condition) {
        // Example business rules - customize based on requirements
        
        // System resources should only have GLOBAL or NONE scope
        if (resource == SecurityResource.SYSTEM && scope != SecurityScope.GLOBAL && scope != SecurityScope.NONE) {
            throw new IllegalArgumentException("System resource permissions must have GLOBAL or NONE scope");
        }
        
        // SELF scope should only be used with specific actions
        if (scope == SecurityScope.SELF && 
            action != SecurityAction.READ && action != SecurityAction.UPDATE) {
            throw new IllegalArgumentException("SELF scope can only be used with READ or update actions");
        }
        
        // MANAGE action should not have conditions
        if (action == SecurityAction.MANAGE && condition != null) {
            throw new IllegalArgumentException("MANAGE action should not have conditions");
        }
    }

    private long countByStatus(String status) {
        return permissionRepository.findAll().stream()
            .filter(p -> status.equals(p.getStatus()))
            .count();
    }
}
