package com.smaile.health.security.service;

import com.smaile.health.domain.Organization;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.security.audit.SecurityAuditService;
import com.smaile.health.security.audit.SecurityEventType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Service for managing organization hierarchy and ensuring single-parent constraint.
 * Provides methods to navigate and validate organizational relationships.
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class OrganizationHierarchyService {

    private final OrganizationRepository organizationRepository;
    private final SecurityAuditService auditService;

    /**
     * Get all parent organizations up to the root
     */
    @Transactional(readOnly = true)
    public List<Organization> getParentOrganizations(Organization organization) {
        List<Organization> parents = new ArrayList<>();
        Organization current = organization.getParent();
        
        while (current != null) {
            parents.add(current);
            current = current.getParent();
        }
        
        return parents;
    }

    /**
     * Get all child organizations (direct children only)
     */
    @Transactional(readOnly = true)
    public List<Organization> getChildOrganizations(Organization organization) {
        return organizationRepository.findByParentId(organization.getId(), null).getContent();
    }

    /**
     * Get all descendant organizations (children, grandchildren, etc.)
     */
    @Transactional(readOnly = true)
    public Set<Organization> getDescendantOrganizations(Organization organization) {
        Set<Organization> descendants = new HashSet<>();
        
        List<Organization> children = getChildOrganizations(organization);
        for (Organization child : children) {
            descendants.add(child);
            descendants.addAll(getDescendantOrganizations(child));
        }
        
        return descendants;
    }

    /**
     * Get the root organization for a given organization
     */
    @Transactional(readOnly = true)
    public Organization getRootOrganization(Organization organization) {
        Organization current = organization;
        
        while (current.getParent() != null) {
            current = current.getParent();
        }
        
        return current;
    }

    /**
     * Get the organizational path from root to the given organization
     */
    @Transactional(readOnly = true)
    public List<Organization> getOrganizationPath(Organization organization) {
        List<Organization> path = new ArrayList<>();
        Organization current = organization;
        
        while (current != null) {
            path.add(0, current); // Add at beginning to maintain root-to-leaf order
            current = current.getParent();
        }
        
        return path;
    }

    /**
     * Get the depth level of an organization (root = 0)
     */
    @Transactional(readOnly = true)
    public int getOrganizationDepth(Organization organization) {
        int depth = 0;
        Organization current = organization.getParent();
        
        while (current != null) {
            depth++;
            current = current.getParent();
        }
        
        return depth;
    }

    /**
     * Check if one organization is an ancestor of another
     */
    @Transactional(readOnly = true)
    public boolean isAncestor(Organization potentialAncestor, Organization organization) {
        Organization current = organization.getParent();
        
        while (current != null) {
            if (current.getId().equals(potentialAncestor.getId())) {
                return true;
            }
            current = current.getParent();
        }
        
        return false;
    }

    /**
     * Check if one organization is a descendant of another
     */
    @Transactional(readOnly = true)
    public boolean isDescendant(Organization potentialDescendant, Organization organization) {
        return isAncestor(organization, potentialDescendant);
    }

    /**
     * Check if two organizations are in the same hierarchy tree
     */
    @Transactional(readOnly = true)
    public boolean areInSameHierarchy(Organization org1, Organization org2) {
        Organization root1 = getRootOrganization(org1);
        Organization root2 = getRootOrganization(org2);
        
        return root1.getId().equals(root2.getId());
    }

    /**
     * Find the common ancestor of two organizations
     */
    @Transactional(readOnly = true)
    public Optional<Organization> findCommonAncestor(Organization org1, Organization org2) {
        if (!areInSameHierarchy(org1, org2)) {
            return Optional.empty();
        }
        
        List<Organization> path1 = getOrganizationPath(org1);
        List<Organization> path2 = getOrganizationPath(org2);
        
        Organization commonAncestor = null;
        int minLength = Math.min(path1.size(), path2.size());
        
        for (int i = 0; i < minLength; i++) {
            if (path1.get(i).getId().equals(path2.get(i).getId())) {
                commonAncestor = path1.get(i);
            } else {
                break;
            }
        }
        
        return Optional.ofNullable(commonAncestor);
    }

    /**
     * Validate organization hierarchy constraints
     */
    public void validateOrganizationHierarchy(Organization organization, Organization newParent) {
        if (newParent == null) {
            return; // Root organization is valid
        }
        
        // Check if organization would become its own ancestor (cycle detection)
        if (organization.getId().equals(newParent.getId())) {
            throw new IllegalArgumentException("Organization cannot be its own parent");
        }
        
        if (isDescendant(newParent, organization)) {
            throw new IllegalArgumentException("Cannot create circular hierarchy: new parent is a descendant");
        }
        
        // Check organization type compatibility
        if (!areOrganizationTypesCompatible(organization, newParent)) {
            throw new IllegalArgumentException("Organization types are not compatible for parent-child relationship");
        }
        
        // Check maximum depth constraint (if needed)
        int newDepth = getOrganizationDepth(newParent) + 1;
        if (newDepth > getMaxAllowedDepth()) {
            throw new IllegalArgumentException("Organization hierarchy would exceed maximum allowed depth");
        }
    }

    /**
     * Change organization parent (maintaining single-parent constraint)
     */
    public void changeOrganizationParent(Organization organization, Organization newParent) {
        validateOrganizationHierarchy(organization, newParent);
        
        Organization oldParent = organization.getParent();
        organization.setParent(newParent);
        
        organizationRepository.save(organization);
        
        // Log the hierarchy change
        String description = String.format("Organization hierarchy changed: %s moved from %s to %s",
                organization.getName(),
                oldParent != null ? oldParent.getName() : "ROOT",
                newParent != null ? newParent.getName() : "ROOT");
        
        auditService.logSecurityEvent(
            SecurityEventType.ORGANIZATION_HIERARCHY_CHANGED,
            description,
            "ORGANIZATION", organization.getId(), organization.getName()
        );
        
        log.info("Organization hierarchy changed: {} moved from {} to {}",
                organization.getName(),
                oldParent != null ? oldParent.getName() : "ROOT",
                newParent != null ? newParent.getName() : "ROOT");
    }

    /**
     * Get all organizations that a user can access based on their organization and permissions
     */
    @Transactional(readOnly = true)
    public Set<Organization> getAccessibleOrganizations(Organization userOrganization, boolean includeChildren, boolean includeParents) {
        Set<Organization> accessible = new HashSet<>();
        
        // Always include the user's own organization
        accessible.add(userOrganization);
        
        if (includeChildren) {
            accessible.addAll(getDescendantOrganizations(userOrganization));
        }
        
        if (includeParents) {
            accessible.addAll(getParentOrganizations(userOrganization));
        }
        
        return accessible;
    }

    /**
     * Check if organization types are compatible for parent-child relationship
     */
    private boolean areOrganizationTypesCompatible(Organization child, Organization parent) {
        // Define business rules for organization type compatibility
        return switch (parent.getType()) {
            case SUPER_SMAILE -> true; // Super Smaile can be parent of any organization
            case IC -> child.getType().name().startsWith("IC_") || child.getType().name().equals("PROFESSIONAL");
            case IC_TPA -> child.getType().name().startsWith("IC_TPA_") || child.getType().name().equals("PROFESSIONAL");
            case SMAILE_TPA -> child.getType().name().startsWith("SMAILE_TPA_") || child.getType().name().equals("PROFESSIONAL");
            case IC_MP, SMAILE_MP -> child.getType().name().equals("PROFESSIONAL");
            default -> false;
        };
    }

    /**
     * Get maximum allowed hierarchy depth
     */
    private int getMaxAllowedDepth() {
        return 5; // Configurable maximum depth
    }

    /**
     * Get organization hierarchy statistics
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getHierarchyStatistics(Organization rootOrganization) {
        Map<String, Object> stats = new HashMap<>();
        
        Set<Organization> allDescendants = getDescendantOrganizations(rootOrganization);
        
        stats.put("totalOrganizations", allDescendants.size() + 1); // +1 for root
        stats.put("maxDepth", calculateMaxDepth(rootOrganization));
        stats.put("directChildren", getChildOrganizations(rootOrganization).size());
        stats.put("organizationTypeDistribution", getOrganizationTypeDistribution(rootOrganization));
        
        return stats;
    }

    private int calculateMaxDepth(Organization organization) {
        int maxDepth = 0;
        
        for (Organization child : getChildOrganizations(organization)) {
            int childDepth = 1 + calculateMaxDepth(child);
            maxDepth = Math.max(maxDepth, childDepth);
        }
        
        return maxDepth;
    }

    private Map<String, Integer> getOrganizationTypeDistribution(Organization rootOrganization) {
        Map<String, Integer> distribution = new HashMap<>();
        
        // Count root organization
        String rootType = rootOrganization.getType().name();
        distribution.put(rootType, distribution.getOrDefault(rootType, 0) + 1);
        
        // Count descendants
        Set<Organization> descendants = getDescendantOrganizations(rootOrganization);
        for (Organization org : descendants) {
            String type = org.getType().name();
            distribution.put(type, distribution.getOrDefault(type, 0) + 1);
        }
        
        return distribution;
    }
}
