package com.smaile.health.security.service;

import com.smaile.health.config.CustomAuthentication;
import com.smaile.health.domain.User;
import com.smaile.health.security.context.SecurityContextBuilder;
import com.smaile.health.security.domain.OrganizationPermissionContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * Service responsible for building and managing authentication context
 * with organization-specific permissions after successful authentication.
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class AuthenticationContextService {

    private final SecurityContextBuilder securityContextBuilder;

    /**
     * Build complete authentication context for a user after successful authentication
     */
    public CustomAuthentication buildAuthenticationContext(User user) {
        log.debug("Building authentication context for user: {}", user.getEmail());
        
        try {
            // Build organization permission contexts
            Map<UUID, OrganizationPermissionContext> permissionContexts = 
                securityContextBuilder.buildOrganizationPermissionContexts(user);
            
            // Build simplified permission map for backward compatibility
            Map<UUID, Set<String>> organizationToPermissionsMap = 
                securityContextBuilder.buildPermissionMap(permissionContexts);
            
            // Build role set
            Set<String> roles = securityContextBuilder.buildRoleSet(permissionContexts);
            
            // Create and populate CustomAuthentication
            CustomAuthentication authentication = new CustomAuthentication();
            authentication.setActor(user);
            authentication.setRoles(roles);
            authentication.setOrganizationToPermissionsMap(organizationToPermissionsMap);
            authentication.setOrganizationPermissionContexts(permissionContexts);
            authentication.setAuthenticated(true);
            
            log.info("Built authentication context for user {} with {} organization contexts", 
                    user.getEmail(), permissionContexts.size());
            
            return authentication;
            
        } catch (Exception e) {
            log.error("Failed to build authentication context for user: {}", user.getEmail(), e);
            throw new RuntimeException("Failed to build authentication context", e);
        }
    }

    /**
     * Refresh authentication context for a user (useful when permissions change)
     */
    public CustomAuthentication refreshAuthenticationContext(CustomAuthentication currentAuth) {
        if (currentAuth == null || currentAuth.getActor() == null) {
            throw new IllegalArgumentException("Invalid authentication context");
        }
        
        log.debug("Refreshing authentication context for user: {}", currentAuth.getActor().getEmail());
        return buildAuthenticationContext(currentAuth.getActor());
    }

    /**
     * Check if user has permission in any organization
     */
    public boolean hasPermissionInAnyOrganization(CustomAuthentication auth, String permission) {
        if (auth == null || auth.getOrganizationPermissionContexts() == null) {
            return false;
        }
        
        return auth.getOrganizationPermissionContexts().values().stream()
            .anyMatch(context -> context.hasPermission(permission));
    }

    /**
     * Check if user has permission in specific organization or inherited from parent
     */
    public boolean hasPermissionInOrganizationHierarchy(CustomAuthentication auth, UUID organizationId, String permission) {
        if (auth == null || auth.getOrganizationPermissionContexts() == null) {
            return false;
        }
        
        // Direct permission in organization
        OrganizationPermissionContext directContext = auth.getOrganizationPermissionContexts().get(organizationId);
        if (directContext != null && directContext.hasPermission(permission)) {
            return true;
        }
        
        // Check inherited permissions (contexts marked as inherited)
        return auth.getOrganizationPermissionContexts().values().stream()
            .filter(OrganizationPermissionContext::isInherited)
            .filter(context -> context.getOrganizationId().equals(organizationId))
            .anyMatch(context -> context.hasPermission(permission));
    }

    /**
     * Get all organizations where user has a specific permission
     */
    public Set<UUID> getOrganizationsWithPermission(CustomAuthentication auth, String permission) {
        if (auth == null || auth.getOrganizationPermissionContexts() == null) {
            return Set.of();
        }
        
        return auth.getOrganizationPermissionContexts().entrySet().stream()
            .filter(entry -> entry.getValue().hasPermission(permission))
            .map(Map.Entry::getKey)
            .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * Get all organizations where user has a specific role
     */
    public Set<UUID> getOrganizationsWithRole(CustomAuthentication auth, String role) {
        if (auth == null || auth.getOrganizationPermissionContexts() == null) {
            return Set.of();
        }
        
        return auth.getOrganizationPermissionContexts().entrySet().stream()
            .filter(entry -> entry.getValue().hasRole(role))
            .map(Map.Entry::getKey)
            .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * Check if user has administrative access in any organization
     */
    public boolean hasAdministrativeAccess(CustomAuthentication auth) {
        if (auth == null || auth.getOrganizationPermissionContexts() == null) {
            return false;
        }
        
        // Check for admin roles or manage permissions
        return auth.getOrganizationPermissionContexts().values().stream()
            .anyMatch(context -> 
                context.getRoles().stream().anyMatch(role -> role.contains("ADMIN")) ||
                context.getPermissions().stream().anyMatch(perm -> perm.contains("manage"))
            );
    }

    /**
     * Get summary of user's access across all organizations
     */
    public Map<String, Object> getAccessSummary(CustomAuthentication auth) {
        if (auth == null || auth.getOrganizationPermissionContexts() == null) {
            return Map.of();
        }
        
        Map<UUID, OrganizationPermissionContext> contexts = auth.getOrganizationPermissionContexts();
        
        int totalOrganizations = contexts.size();
        int directAccess = (int) contexts.values().stream().filter(c -> !c.isInherited()).count();
        int inheritedAccess = (int) contexts.values().stream().filter(OrganizationPermissionContext::isInherited).count();
        
        Set<String> allPermissions = contexts.values().stream()
            .flatMap(context -> context.getPermissions().stream())
            .collect(java.util.stream.Collectors.toSet());
        
        Set<String> allRoles = contexts.values().stream()
            .flatMap(context -> context.getRoles().stream())
            .collect(java.util.stream.Collectors.toSet());
        
        return Map.of(
            "userId", auth.getActor().getId(),
            "userEmail", auth.getActor().getEmail(),
            "totalOrganizations", totalOrganizations,
            "directAccess", directAccess,
            "inheritedAccess", inheritedAccess,
            "totalPermissions", allPermissions.size(),
            "totalRoles", allRoles.size(),
            "hasAdminAccess", hasAdministrativeAccess(auth)
        );
    }

    /**
     * Validate authentication context integrity
     */
    public boolean validateAuthenticationContext(CustomAuthentication auth) {
        if (auth == null) {
            return false;
        }
        
        if (auth.getActor() == null) {
            log.warn("Authentication context missing user actor");
            return false;
        }
        
        if (auth.getOrganizationPermissionContexts() == null) {
            log.warn("Authentication context missing organization permission contexts");
            return false;
        }
        
        // Validate consistency between old and new permission maps
        if (auth.getOrganizationToPermissionsMap() != null) {
            Map<UUID, Set<String>> expectedMap = securityContextBuilder.buildPermissionMap(
                auth.getOrganizationPermissionContexts());
            
            if (!auth.getOrganizationToPermissionsMap().equals(expectedMap)) {
                log.warn("Inconsistency detected between permission maps for user: {}", 
                        auth.getActor().getEmail());
                return false;
            }
        }
        
        return true;
    }
}
