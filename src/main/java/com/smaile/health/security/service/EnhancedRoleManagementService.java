package com.smaile.health.security.service;

import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleScope;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
import com.smaile.health.exception.SmaileRuntimeException;
// Audit imports removed as per requirements
import com.smaile.health.security.domain.EnhancedPermission;
import com.smaile.health.security.domain.EnhancedRole;
import com.smaile.health.security.domain.EnhancedUserRole;
import com.smaile.health.security.repository.EnhancedRoleRepository;
import com.smaile.health.security.repository.EnhancedUserRoleRepository;
import com.smaile.health.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for managing enhanced roles and role assignments.
 * Provides enterprise-level role management with validation, audit, and hierarchy support.
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class EnhancedRoleManagementService {

    private final EnhancedRoleRepository roleRepository;
    private final EnhancedUserRoleRepository userRoleRepository;
    // Audit service removed as per requirements
    private final RoleHierarchyService roleHierarchyService;

    /**
     * Create a new role for a specific organization
     */
    public EnhancedRole createRole(String name, String code, String description,
                                  Organization organization, RoleScope scope,
                                  boolean inheritable) {
        validateRoleCreation(name, code, organization, scope);

        // Check if role already exists in this organization
        Optional<EnhancedRole> existing = roleRepository.findByCodeAndOrganizationType(code, organization.getType());
        if (existing.isPresent() && existing.get().getOrganization().getId().equals(organization.getId())) {
            throw new SmaileRuntimeException("Role already exists: " + code + " in organization " + organization.getName());
        }

        EnhancedRole role = EnhancedRole.builder()
                .id(UUID.randomUUID())
                .name(name)
                .code(code)
                .description(description)
                .organization(organization)
                .organizationType(organization.getType())
                .scope(scope)
                .hierarchyLevel(0)
                .systemRole(false)
                .inheritable(inheritable)
                .status("ACTIVE")
                .permissions(new HashSet<>())
                .userRoles(new HashSet<>())
                .build();

        // Set hierarchy level based on organization hierarchy
        role.setHierarchyLevelFromOrganization();

        EnhancedRole savedRole = roleRepository.save(role);

        // Audit logging removed as per requirements

        log.info("Role created: {} ({}) in organization {}", savedRole.getName(), savedRole.getCode(), organization.getName());
        return savedRole;
    }

    /**
     * Update role information
     */
    public EnhancedRole updateRole(UUID roleId, String name, String description, boolean inheritable) {
        EnhancedRole role = getRoleById(roleId);
        
        if (role.isSystemRole()) {
            throw new SmaileRuntimeException("Cannot modify system role: " + role.getCode());
        }
        
        Map<String, Object> beforeState = Map.of(
            "name", role.getName(),
            "description", role.getDescription() != null ? role.getDescription() : "",
            "inheritable", role.isInheritable()
        );
        
        role.setName(name);
        role.setDescription(description);
        role.setInheritable(inheritable);
        
        EnhancedRole updatedRole = roleRepository.save(role);
        
        Map<String, Object> afterState = Map.of(
            "name", updatedRole.getName(),
            "description", updatedRole.getDescription() != null ? updatedRole.getDescription() : "",
            "inheritable", updatedRole.isInheritable()
        );
        
        Map<String, Object> details = Map.of(
            "beforeState", beforeState,
            "afterState", afterState
        );
        
        // Audit logging removed as per requirements
        
        log.info("Role updated: {} ({})", updatedRole.getName(), updatedRole.getCode());
        return updatedRole;
    }

    /**
     * Delete role (soft delete by setting status to INACTIVE)
     */
    public void deleteRole(UUID roleId) {
        EnhancedRole role = getRoleById(roleId);
        
        if (role.isSystemRole()) {
            throw new SmaileRuntimeException("Cannot delete system role: " + role.getCode());
        }
        
        // Check if role is assigned to any users
        long activeAssignments = userRoleRepository.countActiveRolesByUser(roleId);
        if (activeAssignments > 0) {
            throw new SmaileRuntimeException("Cannot delete role that is assigned to users: " + role.getCode());
        }
        
        // Check if role has child roles
        if (!role.getChildRoles().isEmpty()) {
            throw new SmaileRuntimeException("Cannot delete role that has child roles: " + role.getCode());
        }
        
        role.setStatus("INACTIVE");
        roleRepository.save(role);
        
        // Audit logging removed as per requirements
        
        log.info("Role deleted: {} ({})", role.getName(), role.getCode());
    }

    /**
     * Assign permission to role
     */
    public void assignPermissionToRole(UUID roleId, UUID permissionId) {
        EnhancedRole role = getRoleById(roleId);
        EnhancedPermission permission = getPermissionById(permissionId);
        
        if (role.getPermissions().contains(permission)) {
            throw new SmaileRuntimeException("Permission already assigned to role");
        }
        
        role.addPermission(permission);
        roleRepository.save(role);
        
        // Audit logging removed as per requirements
        
        log.info("Permission {} assigned to role {}", permission.getPermissionString(), role.getName());
    }

    /**
     * Remove permission from role
     */
    public void removePermissionFromRole(UUID roleId, UUID permissionId) {
        EnhancedRole role = getRoleById(roleId);
        EnhancedPermission permission = getPermissionById(permissionId);
        
        if (!role.getPermissions().contains(permission)) {
            throw new SmaileRuntimeException("Permission not assigned to role");
        }
        
        role.removePermission(permission);
        roleRepository.save(role);
        
        // Audit logging removed as per requirements
        
        log.info("Permission {} removed from role {}", permission.getPermissionString(), role.getName());
    }

    /**
     * Assign role to user in organization
     */
    public EnhancedUserRole assignRoleToUser(UUID userId, UUID roleId, UUID organizationId,
                                           OffsetDateTime validFrom, OffsetDateTime validTo,
                                           String reason, String notes) {
        User user = getUserById(userId);
        EnhancedRole role = getRoleById(roleId);
        Organization organization = getOrganizationById(organizationId);
        User assignedBy = getCurrentUser();

        // Validate role assignment within organizational constraints
        roleHierarchyService.validateRoleAssignment(role, organization);
        validateRoleAssignment(user, role, organization);

        // Check if assignment already exists
        Optional<EnhancedUserRole> existing = userRoleRepository.findByUserIdAndRoleIdAndOrganizationId(
            userId, roleId, organizationId);

        if (existing.isPresent() && existing.get().isActive()) {
            throw new SmaileRuntimeException("User already has this role in the organization");
        }

        EnhancedUserRole userRole = EnhancedUserRole.builder()
                .id(UUID.randomUUID())
                .user(user)
                .role(role)
                .organization(organization)
                .status("ACTIVE")
                .validFrom(validFrom != null ? validFrom : OffsetDateTime.now())
                .validTo(validTo)
                .assignedByUser(assignedBy)
                .assignmentReason(reason)
                .assignmentNotes(notes)
                .build();

        EnhancedUserRole savedUserRole = userRoleRepository.save(userRole);

        // Audit logging removed as per requirements

        log.info("Role {} assigned to user {} in organization {}",
                role.getName(), user.getFullName(), organization.getName());

        return savedUserRole;
    }

    /**
     * Revoke role from user
     */
    public void revokeRoleFromUser(UUID userRoleId, String reason) {
        EnhancedUserRole userRole = getUserRoleById(userRoleId);
        User revokedBy = getCurrentUser();
        
        if (!userRole.isActive()) {
            throw new SmaileRuntimeException("Role assignment is not active");
        }
        
        userRole.revoke(revokedBy, reason);
        userRoleRepository.save(userRole);
        
        // Audit logging removed as per requirements
        
        log.info("Role {} revoked from user {} in organization {}", 
                userRole.getRole().getName(), 
                userRole.getUser().getFullName(), 
                userRole.getOrganization().getName());
    }

    /**
     * Get role by ID
     */
    @Transactional(readOnly = true)
    public EnhancedRole getRoleById(UUID roleId) {
        return roleRepository.findById(roleId)
            .orElseThrow(() -> new SmaileRuntimeException("Role not found: " + roleId));
    }

    /**
     * Get role by code and organization type
     */
    @Transactional(readOnly = true)
    public Optional<EnhancedRole> getRoleByCodeAndOrganizationType(String code, OrganizationType organizationType) {
        return roleRepository.findByCodeAndOrganizationType(code, organizationType);
    }

    /**
     * Get all active roles for organization type
     */
    @Transactional(readOnly = true)
    public List<EnhancedRole> getRolesByOrganizationType(OrganizationType organizationType) {
        return roleRepository.findByOrganizationTypeAndStatus(organizationType, "ACTIVE");
    }

    /**
     * Get assignable roles for organization type
     */
    @Transactional(readOnly = true)
    public List<EnhancedRole> getAssignableRoles(OrganizationType organizationType) {
        return roleRepository.findAssignableRoles(organizationType, "ACTIVE");
    }

    /**
     * Search roles by name
     */
    @Transactional(readOnly = true)
    public Page<EnhancedRole> searchRoles(String namePattern, Pageable pageable) {
        return roleRepository.findByNameContainingIgnoreCase(namePattern, "ACTIVE", pageable);
    }

    /**
     * Get user role assignments
     */
    @Transactional(readOnly = true)
    public List<EnhancedUserRole> getUserRoleAssignments(UUID userId) {
        return userRoleRepository.findByUserIdOrderByValidFromDesc(userId);
    }

    /**
     * Get active user roles
     */
    @Transactional(readOnly = true)
    public List<EnhancedUserRole> getActiveUserRoles(UUID userId) {
        return userRoleRepository.findActiveRolesByUser(userId);
    }

    /**
     * Get role statistics
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getRoleStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // Total roles
        long totalRoles = roleRepository.count();
        stats.put("totalRoles", totalRoles);
        
        // Active roles
        long activeRoles = roleRepository.findByStatus("ACTIVE").size();
        stats.put("activeRoles", activeRoles);
        
        // System roles
        long systemRoles = roleRepository.findBySystemRoleTrueAndStatus("ACTIVE").size();
        stats.put("systemRoles", systemRoles);
        
        // Inheritable roles
        long inheritableRoles = roleRepository.findByInheritableTrueAndStatus("ACTIVE").size();
        stats.put("inheritableRoles", inheritableRoles);
        
        // Statistics by organization type
        List<Object[]> orgTypeStats = roleRepository.getRoleStatisticsByOrganizationType("ACTIVE");
        Map<String, Map<String, Object>> orgTypeStatsMap = new HashMap<>();
        for (Object[] stat : orgTypeStats) {
            Map<String, Object> orgTypeStat = new HashMap<>();
            orgTypeStat.put("roleCount", stat[1]);
            orgTypeStat.put("avgPermissions", stat[2]);
            orgTypeStatsMap.put(stat[0].toString(), orgTypeStat);
        }
        stats.put("organizationTypeStatistics", orgTypeStatsMap);
        
        return stats;
    }

    private void validateRoleCreation(String name, String code, Organization organization, RoleScope scope) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Role name cannot be null or empty");
        }
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("Role code cannot be null or empty");
        }
        if (organization == null) {
            throw new IllegalArgumentException("Organization cannot be null");
        }
        if (scope == null) {
            throw new IllegalArgumentException("Role scope cannot be null");
        }
    }

    private void validateRoleAssignment(User user, EnhancedRole role, Organization organization) {
        // Check if user is active
        if (!"ACTIVE".equals(user.getStatus())) {
            throw new SmaileRuntimeException("Cannot assign role to inactive user");
        }

        // Check if role is active
        if (!"ACTIVE".equals(role.getStatus())) {
            throw new SmaileRuntimeException("Cannot assign inactive role");
        }

        // Additional validation is now handled by roleHierarchyService.validateRoleAssignment()
    }

    // Removed - compatibility validation is now handled by RoleHierarchyService

    // Helper methods to get entities (these would typically be injected services)
    private User getUserById(UUID userId) {
        // This would be injected UserService
        throw new UnsupportedOperationException("UserService integration needed");
    }

    private Organization getOrganizationById(UUID organizationId) {
        // This would be injected OrganizationService
        throw new UnsupportedOperationException("OrganizationService integration needed");
    }

    private EnhancedPermission getPermissionById(UUID permissionId) {
        // This would be injected PermissionService
        throw new UnsupportedOperationException("PermissionService integration needed");
    }

    private EnhancedUserRole getUserRoleById(UUID userRoleId) {
        return userRoleRepository.findById(userRoleId)
            .orElseThrow(() -> new SmaileRuntimeException("User role assignment not found: " + userRoleId));
    }

    private User getCurrentUser() {
        return SecurityUtils.getActorContext() != null ? 
            SecurityUtils.getActorContext().getActor() : null;
    }
}
