package com.smaile.health.security.service;

import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleScope;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.security.audit.SecurityAuditService;
import com.smaile.health.security.audit.SecurityEventType;
import com.smaile.health.security.domain.EnhancedPermission;
import com.smaile.health.security.domain.EnhancedRole;
import com.smaile.health.security.domain.EnhancedUserRole;
import com.smaile.health.security.repository.EnhancedRoleRepository;
import com.smaile.health.security.repository.EnhancedUserRoleRepository;
import com.smaile.health.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for managing enhanced roles and role assignments.
 * Provides enterprise-level role management with validation, audit, and hierarchy support.
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class EnhancedRoleManagementService {

    private final EnhancedRoleRepository roleRepository;
    private final EnhancedUserRoleRepository userRoleRepository;
    private final SecurityAuditService auditService;
    private final RoleHierarchyService roleHierarchyService;

    /**
     * Create a new role
     */
    public EnhancedRole createRole(String name, String code, String description, 
                                  OrganizationType organizationType, RoleScope scope, 
                                  boolean inheritable) {
        validateRoleCreation(name, code, organizationType, scope);
        
        // Check if role already exists
        Optional<EnhancedRole> existing = roleRepository.findByCodeAndOrganizationType(code, organizationType);
        if (existing.isPresent()) {
            throw new SmaileRuntimeException("Role already exists: " + code + " for " + organizationType);
        }
        
        EnhancedRole role = EnhancedRole.builder()
                .id(UUID.randomUUID())
                .name(name)
                .code(code)
                .description(description)
                .organizationType(organizationType)
                .scope(scope)
                .hierarchyLevel(0)
                .systemRole(false)
                .inheritable(inheritable)
                .status("ACTIVE")
                .permissions(new HashSet<>())
                .childRoles(new HashSet<>())
                .userRoles(new HashSet<>())
                .build();
        
        EnhancedRole savedRole = roleRepository.save(role);
        
        auditService.logSecurityEvent(
            SecurityEventType.ROLE_CREATED,
            "Role created: " + savedRole.getName() + " (" + savedRole.getCode() + ")",
            "ROLE", savedRole.getId(), savedRole.getName()
        );
        
        log.info("Role created: {} ({})", savedRole.getName(), savedRole.getCode());
        return savedRole;
    }

    /**
     * Update role information
     */
    public EnhancedRole updateRole(UUID roleId, String name, String description, boolean inheritable) {
        EnhancedRole role = getRoleById(roleId);
        
        if (role.isSystemRole()) {
            throw new SmaileRuntimeException("Cannot modify system role: " + role.getCode());
        }
        
        Map<String, Object> beforeState = Map.of(
            "name", role.getName(),
            "description", role.getDescription() != null ? role.getDescription() : "",
            "inheritable", role.isInheritable()
        );
        
        role.setName(name);
        role.setDescription(description);
        role.setInheritable(inheritable);
        
        EnhancedRole updatedRole = roleRepository.save(role);
        
        Map<String, Object> afterState = Map.of(
            "name", updatedRole.getName(),
            "description", updatedRole.getDescription() != null ? updatedRole.getDescription() : "",
            "inheritable", updatedRole.isInheritable()
        );
        
        Map<String, Object> details = Map.of(
            "beforeState", beforeState,
            "afterState", afterState
        );
        
        auditService.logSecurityEvent(
            SecurityEventType.ROLE_UPDATED,
            "Role updated: " + updatedRole.getName() + " (" + updatedRole.getCode() + ")",
            "ROLE", updatedRole.getId(), updatedRole.getName(),
            details, true, null
        );
        
        log.info("Role updated: {} ({})", updatedRole.getName(), updatedRole.getCode());
        return updatedRole;
    }

    /**
     * Delete role (soft delete by setting status to INACTIVE)
     */
    public void deleteRole(UUID roleId) {
        EnhancedRole role = getRoleById(roleId);
        
        if (role.isSystemRole()) {
            throw new SmaileRuntimeException("Cannot delete system role: " + role.getCode());
        }
        
        // Check if role is assigned to any users
        long activeAssignments = userRoleRepository.countActiveRolesByUser(roleId);
        if (activeAssignments > 0) {
            throw new SmaileRuntimeException("Cannot delete role that is assigned to users: " + role.getCode());
        }
        
        // Check if role has child roles
        if (!role.getChildRoles().isEmpty()) {
            throw new SmaileRuntimeException("Cannot delete role that has child roles: " + role.getCode());
        }
        
        role.setStatus("INACTIVE");
        roleRepository.save(role);
        
        auditService.logSecurityEvent(
            SecurityEventType.ROLE_DELETED,
            "Role deleted: " + role.getName() + " (" + role.getCode() + ")",
            "ROLE", role.getId(), role.getName()
        );
        
        log.info("Role deleted: {} ({})", role.getName(), role.getCode());
    }

    /**
     * Assign permission to role
     */
    public void assignPermissionToRole(UUID roleId, UUID permissionId) {
        EnhancedRole role = getRoleById(roleId);
        EnhancedPermission permission = getPermissionById(permissionId);
        
        if (role.getPermissions().contains(permission)) {
            throw new SmaileRuntimeException("Permission already assigned to role");
        }
        
        role.addPermission(permission);
        roleRepository.save(role);
        
        auditService.logSecurityEvent(
            SecurityEventType.PERMISSION_GRANTED,
            "Permission granted to role: " + permission.getPermissionString() + " -> " + role.getName(),
            "ROLE", role.getId(), role.getName()
        );
        
        log.info("Permission {} assigned to role {}", permission.getPermissionString(), role.getName());
    }

    /**
     * Remove permission from role
     */
    public void removePermissionFromRole(UUID roleId, UUID permissionId) {
        EnhancedRole role = getRoleById(roleId);
        EnhancedPermission permission = getPermissionById(permissionId);
        
        if (!role.getPermissions().contains(permission)) {
            throw new SmaileRuntimeException("Permission not assigned to role");
        }
        
        role.removePermission(permission);
        roleRepository.save(role);
        
        auditService.logSecurityEvent(
            SecurityEventType.PERMISSION_REMOVED,
            "Permission removed from role: " + permission.getPermissionString() + " <- " + role.getName(),
            "ROLE", role.getId(), role.getName()
        );
        
        log.info("Permission {} removed from role {}", permission.getPermissionString(), role.getName());
    }

    /**
     * Assign role to user in organization
     */
    public EnhancedUserRole assignRoleToUser(UUID userId, UUID roleId, UUID organizationId, 
                                           OffsetDateTime validFrom, OffsetDateTime validTo, 
                                           String reason, String notes) {
        User user = getUserById(userId);
        EnhancedRole role = getRoleById(roleId);
        Organization organization = getOrganizationById(organizationId);
        User assignedBy = getCurrentUser();
        
        validateRoleAssignment(user, role, organization);
        
        // Check if assignment already exists
        Optional<EnhancedUserRole> existing = userRoleRepository.findByUserIdAndRoleIdAndOrganizationId(
            userId, roleId, organizationId);
        
        if (existing.isPresent() && existing.get().isActive()) {
            throw new SmaileRuntimeException("User already has this role in the organization");
        }
        
        EnhancedUserRole userRole = EnhancedUserRole.builder()
                .id(UUID.randomUUID())
                .user(user)
                .role(role)
                .organization(organization)
                .status("ACTIVE")
                .validFrom(validFrom != null ? validFrom : OffsetDateTime.now())
                .validTo(validTo)
                .assignedByUser(assignedBy)
                .assignmentReason(reason)
                .assignmentNotes(notes)
                .build();
        
        EnhancedUserRole savedUserRole = userRoleRepository.save(userRole);
        
        auditService.logRoleAssignment(user, role.getName(), organization, assignedBy, reason);
        
        log.info("Role {} assigned to user {} in organization {}", 
                role.getName(), user.getFullName(), organization.getName());
        
        return savedUserRole;
    }

    /**
     * Revoke role from user
     */
    public void revokeRoleFromUser(UUID userRoleId, String reason) {
        EnhancedUserRole userRole = getUserRoleById(userRoleId);
        User revokedBy = getCurrentUser();
        
        if (!userRole.isActive()) {
            throw new SmaileRuntimeException("Role assignment is not active");
        }
        
        userRole.revoke(revokedBy, reason);
        userRoleRepository.save(userRole);
        
        auditService.logRoleRevocation(
            userRole.getUser(), 
            userRole.getRole().getName(), 
            userRole.getOrganization(), 
            revokedBy, 
            reason
        );
        
        log.info("Role {} revoked from user {} in organization {}", 
                userRole.getRole().getName(), 
                userRole.getUser().getFullName(), 
                userRole.getOrganization().getName());
    }

    /**
     * Get role by ID
     */
    @Transactional(readOnly = true)
    public EnhancedRole getRoleById(UUID roleId) {
        return roleRepository.findById(roleId)
            .orElseThrow(() -> new SmaileRuntimeException("Role not found: " + roleId));
    }

    /**
     * Get role by code and organization type
     */
    @Transactional(readOnly = true)
    public Optional<EnhancedRole> getRoleByCodeAndOrganizationType(String code, OrganizationType organizationType) {
        return roleRepository.findByCodeAndOrganizationType(code, organizationType);
    }

    /**
     * Get all active roles for organization type
     */
    @Transactional(readOnly = true)
    public List<EnhancedRole> getRolesByOrganizationType(OrganizationType organizationType) {
        return roleRepository.findByOrganizationTypeAndStatus(organizationType, "ACTIVE");
    }

    /**
     * Get assignable roles for organization type
     */
    @Transactional(readOnly = true)
    public List<EnhancedRole> getAssignableRoles(OrganizationType organizationType) {
        return roleRepository.findAssignableRoles(organizationType, "ACTIVE");
    }

    /**
     * Search roles by name
     */
    @Transactional(readOnly = true)
    public Page<EnhancedRole> searchRoles(String namePattern, Pageable pageable) {
        return roleRepository.findByNameContainingIgnoreCase(namePattern, "ACTIVE", pageable);
    }

    /**
     * Get user role assignments
     */
    @Transactional(readOnly = true)
    public List<EnhancedUserRole> getUserRoleAssignments(UUID userId) {
        return userRoleRepository.findByUserIdOrderByValidFromDesc(userId);
    }

    /**
     * Get active user roles
     */
    @Transactional(readOnly = true)
    public List<EnhancedUserRole> getActiveUserRoles(UUID userId) {
        return userRoleRepository.findActiveRolesByUser(userId);
    }

    /**
     * Get role statistics
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getRoleStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // Total roles
        long totalRoles = roleRepository.count();
        stats.put("totalRoles", totalRoles);
        
        // Active roles
        long activeRoles = roleRepository.findByStatus("ACTIVE").size();
        stats.put("activeRoles", activeRoles);
        
        // System roles
        long systemRoles = roleRepository.findBySystemRoleTrueAndStatus("ACTIVE").size();
        stats.put("systemRoles", systemRoles);
        
        // Inheritable roles
        long inheritableRoles = roleRepository.findByInheritableTrueAndStatus("ACTIVE").size();
        stats.put("inheritableRoles", inheritableRoles);
        
        // Statistics by organization type
        List<Object[]> orgTypeStats = roleRepository.getRoleStatisticsByOrganizationType("ACTIVE");
        Map<String, Map<String, Object>> orgTypeStatsMap = new HashMap<>();
        for (Object[] stat : orgTypeStats) {
            Map<String, Object> orgTypeStat = new HashMap<>();
            orgTypeStat.put("roleCount", stat[1]);
            orgTypeStat.put("avgPermissions", stat[2]);
            orgTypeStatsMap.put(stat[0].toString(), orgTypeStat);
        }
        stats.put("organizationTypeStatistics", orgTypeStatsMap);
        
        return stats;
    }

    private void validateRoleCreation(String name, String code, OrganizationType organizationType, RoleScope scope) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Role name cannot be null or empty");
        }
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("Role code cannot be null or empty");
        }
        if (organizationType == null) {
            throw new IllegalArgumentException("Organization type cannot be null");
        }
        if (scope == null) {
            throw new IllegalArgumentException("Role scope cannot be null");
        }
    }

    private void validateRoleAssignment(User user, EnhancedRole role, Organization organization) {
        // Check if role is compatible with organization type
        if (!isRoleCompatibleWithOrganization(role, organization)) {
            throw new SmaileRuntimeException("Role " + role.getCode() + " is not compatible with organization type " + 
                organization.getType());
        }
        
        // Check if user is active
        if (!"ACTIVE".equals(user.getStatus())) {
            throw new SmaileRuntimeException("Cannot assign role to inactive user");
        }
        
        // Check if role is active
        if (!"ACTIVE".equals(role.getStatus())) {
            throw new SmaileRuntimeException("Cannot assign inactive role");
        }
    }

    private boolean isRoleCompatibleWithOrganization(EnhancedRole role, Organization organization) {
        // Super Smaile roles can be assigned to any organization
        if (role.getOrganizationType() == OrganizationType.SUPER_SMAILE) {
            return true;
        }
        
        // Role organization type must match or be compatible with organization type
        return role.getOrganizationType() == organization.getType() ||
               isOrganizationTypeCompatible(role.getOrganizationType(), organization.getType());
    }

    private boolean isOrganizationTypeCompatible(OrganizationType roleOrgType, OrganizationType orgType) {
        // Define compatibility rules based on business requirements
        return switch (roleOrgType) {
            case IC -> orgType.name().startsWith("IC_");
            case SMAILE_TPA -> orgType.name().startsWith("SMAILE_");
            default -> false;
        };
    }

    // Helper methods to get entities (these would typically be injected services)
    private User getUserById(UUID userId) {
        // This would be injected UserService
        throw new UnsupportedOperationException("UserService integration needed");
    }

    private Organization getOrganizationById(UUID organizationId) {
        // This would be injected OrganizationService
        throw new UnsupportedOperationException("OrganizationService integration needed");
    }

    private EnhancedPermission getPermissionById(UUID permissionId) {
        // This would be injected PermissionService
        throw new UnsupportedOperationException("PermissionService integration needed");
    }

    private EnhancedUserRole getUserRoleById(UUID userRoleId) {
        return userRoleRepository.findById(userRoleId)
            .orElseThrow(() -> new SmaileRuntimeException("User role assignment not found: " + userRoleId));
    }

    private User getCurrentUser() {
        return SecurityUtils.getActorContext() != null ? 
            SecurityUtils.getActorContext().getActor() : null;
    }
}
