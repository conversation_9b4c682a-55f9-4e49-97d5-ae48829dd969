package com.smaile.health.security.service;

import com.smaile.health.config.CustomAuthentication;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
import com.smaile.health.security.domain.OrganizationPermissionContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.UUID;

/**
 * Permission evaluator that uses organization-specific permission contexts
 * built during authentication to evaluate permissions.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class OrganizationPermissionEvaluator implements PermissionEvaluator {

    private final AuthenticationContextService authenticationContextService;

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        if (!(authentication instanceof CustomAuthentication customAuth)) {
            log.warn("Authentication is not CustomAuthentication type");
            return false;
        }

        try {
            return evaluatePermission(customAuth, targetDomainObject, permission.toString());
        } catch (Exception e) {
            log.error("Error evaluating permission: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        if (!(authentication instanceof CustomAuthentication customAuth)) {
            log.warn("Authentication is not CustomAuthentication type");
            return false;
        }

        try {
            UUID organizationId = null;
            if (targetId != null) {
                try {
                    organizationId = UUID.fromString(targetId.toString());
                } catch (IllegalArgumentException e) {
                    log.debug("Target ID is not a valid UUID: {}", targetId);
                }
            }
            
            return evaluatePermissionById(customAuth, organizationId, targetType, permission.toString());
        } catch (Exception e) {
            log.error("Error evaluating permission by ID: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Evaluate permission with domain object context
     */
    private boolean evaluatePermission(CustomAuthentication authentication, Object targetDomainObject, String permission) {
        User user = authentication.getActor();
        
        // Super admin has access to everything
        if (isSuperAdmin(authentication)) {
            log.debug("Super admin access granted for user: {}", user.getEmail());
            return true;
        }

        // Extract organization ID from target domain object
        UUID organizationId = extractOrganizationId(targetDomainObject);
        
        if (organizationId != null) {
            // Check permission in specific organization
            return hasPermissionInOrganization(authentication, organizationId, permission);
        } else {
            // Check permission in any organization
            return authenticationContextService.hasPermissionInAnyOrganization(authentication, permission);
        }
    }

    /**
     * Evaluate permission by organization ID
     */
    private boolean evaluatePermissionById(CustomAuthentication authentication, UUID organizationId, 
                                         String targetType, String permission) {
        User user = authentication.getActor();
        
        // Super admin has access to everything
        if (isSuperAdmin(authentication)) {
            log.debug("Super admin access granted for user: {}", user.getEmail());
            return true;
        }

        if (organizationId != null) {
            return hasPermissionInOrganization(authentication, organizationId, permission);
        } else {
            return authenticationContextService.hasPermissionInAnyOrganization(authentication, permission);
        }
    }

    /**
     * Check if user has permission in specific organization (including inheritance)
     */
    private boolean hasPermissionInOrganization(CustomAuthentication authentication, UUID organizationId, String permission) {
        // Check direct permission in organization
        if (authentication.hasPermissionInOrganization(organizationId, permission)) {
            log.debug("Direct permission '{}' granted in organization {} for user: {}", 
                     permission, organizationId, authentication.getActor().getEmail());
            return true;
        }
        
        // Check inherited permission through organization hierarchy
        if (authenticationContextService.hasPermissionInOrganizationHierarchy(authentication, organizationId, permission)) {
            log.debug("Inherited permission '{}' granted in organization {} for user: {}", 
                     permission, organizationId, authentication.getActor().getEmail());
            return true;
        }
        
        log.debug("Permission '{}' denied in organization {} for user: {}", 
                 permission, organizationId, authentication.getActor().getEmail());
        return false;
    }

    /**
     * Check if user is super admin
     */
    private boolean isSuperAdmin(CustomAuthentication authentication) {
        return authentication.getRoles() != null && 
               authentication.getRoles().contains(RoleEnum.SUPER_SMAILE_ADMIN.name());
    }

    /**
     * Extract organization ID from domain object
     */
    private UUID extractOrganizationId(Object targetDomainObject) {
        if (targetDomainObject == null) {
            return null;
        }

        if (targetDomainObject instanceof UUID) {
            return (UUID) targetDomainObject;
        }

        if (targetDomainObject instanceof String) {
            try {
                return UUID.fromString((String) targetDomainObject);
            } catch (IllegalArgumentException e) {
                log.debug("Target domain object is not a valid UUID: {}", targetDomainObject);
            }
        }

        // Extract from domain objects
        if (targetDomainObject instanceof Organization org) {
            return org.getId();
        }

        // Add more domain object types as needed
        return null;
    }

    /**
     * Check if user has any administrative role
     */
    public boolean hasAdministrativeRole(CustomAuthentication authentication) {
        return authenticationContextService.hasAdministrativeAccess(authentication);
    }

    /**
     * Check if user has specific role in organization
     */
    public boolean hasRoleInOrganization(CustomAuthentication authentication, UUID organizationId, String role) {
        return authentication.hasRoleInOrganization(organizationId, role);
    }

    /**
     * Check if user has access to organization (direct or inherited)
     */
    public boolean hasAccessToOrganization(CustomAuthentication authentication, UUID organizationId) {
        OrganizationPermissionContext context = authentication.getOrganizationContext(organizationId);
        return context != null && (!context.getPermissions().isEmpty() || !context.getRoles().isEmpty());
    }

    /**
     * Get user's permission level in organization (for UI/display purposes)
     */
    public String getPermissionLevel(CustomAuthentication authentication, UUID organizationId) {
        OrganizationPermissionContext context = authentication.getOrganizationContext(organizationId);
        
        if (context == null) {
            return "NONE";
        }
        
        if (isSuperAdmin(authentication)) {
            return "SUPER_ADMIN";
        }
        
        // Check for admin roles
        if (context.getRoles().stream().anyMatch(role -> role.contains("ADMIN"))) {
            return "ADMIN";
        }
        
        // Check for manager roles
        if (context.getRoles().stream().anyMatch(role -> role.contains("MANAGER"))) {
            return "MANAGER";
        }
        
        // Check for manage permissions
        if (context.getPermissions().stream().anyMatch(perm -> perm.contains("manage"))) {
            return "MANAGE";
        }
        
        // Check for write permissions
        if (context.getPermissions().stream().anyMatch(perm -> perm.contains("create") || perm.contains("update") || perm.contains("delete"))) {
            return "WRITE";
        }
        
        // Check for read permissions
        if (context.getPermissions().stream().anyMatch(perm -> perm.contains("read") || perm.contains("view"))) {
            return "READ";
        }
        
        return context.isInherited() ? "INHERITED" : "LIMITED";
    }

    /**
     * Validate permission string format
     */
    private boolean isValidPermissionFormat(String permission) {
        // Basic validation - can be enhanced based on your permission format
        return permission != null && !permission.trim().isEmpty() && permission.contains(":");
    }
}
