package com.smaile.health.security.service;

import com.smaile.health.constants.OrganizationType;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
// Audit imports removed as per requirements
import com.smaile.health.security.domain.EnhancedPermission;
import com.smaile.health.security.domain.EnhancedRole;
import com.smaile.health.security.domain.EnhancedUserRole;
import com.smaile.health.security.repository.EnhancedRoleRepository;
import com.smaile.health.security.repository.EnhancedUserRoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for managing role hierarchy and inheritance.
 * Handles role relationships, permission inheritance, and organizational role propagation.
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class RoleHierarchyService {

    private final EnhancedRoleRepository roleRepository;
    private final EnhancedUserRoleRepository userRoleRepository;
    // Audit service removed as per requirements
    private final OrganizationHierarchyService organizationHierarchyService;

    /**
     * Get all effective permissions for a user across all organizations
     */
    @Transactional(readOnly = true)
    public Set<EnhancedPermission> getEffectivePermissions(User user) {
        Set<EnhancedPermission> allPermissions = new HashSet<>();

        // Get all active user roles
        List<EnhancedUserRole> activeUserRoles = userRoleRepository.findActiveRolesByUser(user.getId());

        for (EnhancedUserRole userRole : activeUserRoles) {
            // Get permissions from the role itself (no role hierarchy inheritance)
            Set<EnhancedPermission> rolePermissions = userRole.getRole().getAllPermissions();
            allPermissions.addAll(rolePermissions);

            // Get inherited permissions from parent organizations only
            Set<EnhancedPermission> inheritedPermissions = getInheritedPermissionsFromOrganizations(
                userRole.getUser(), userRole.getOrganization());
            allPermissions.addAll(inheritedPermissions);
        }

        return allPermissions;
    }

    /**
     * Get effective permissions for a user in a specific organization
     */
    @Transactional(readOnly = true)
    public Set<EnhancedPermission> getEffectivePermissions(User user, Organization organization) {
        Set<EnhancedPermission> permissions = new HashSet<>();

        // Get user's roles in this organization
        List<EnhancedUserRole> userRoles = userRoleRepository.findActiveRolesByUserAndOrganization(
            user.getId(), organization.getId());

        for (EnhancedUserRole userRole : userRoles) {
            permissions.addAll(userRole.getRole().getAllPermissions());
        }

        // Add inherited permissions from parent organizations only
        permissions.addAll(getInheritedPermissionsFromOrganizations(user, organization));

        return permissions;
    }

    /**
     * Get inherited permissions from parent organizations (organizational hierarchy only)
     */
    @Transactional(readOnly = true)
    public Set<EnhancedPermission> getInheritedPermissionsFromOrganizations(User user, Organization organization) {
        Set<EnhancedPermission> inheritedPermissions = new HashSet<>();

        // Get parent organizations
        List<Organization> parentOrganizations = organizationHierarchyService.getParentOrganizations(organization);

        for (Organization parentOrg : parentOrganizations) {
            // Get user's roles in parent organization
            List<EnhancedUserRole> parentRoles = userRoleRepository.findActiveRolesByUserAndOrganization(
                user.getId(), parentOrg.getId());

            for (EnhancedUserRole parentUserRole : parentRoles) {
                EnhancedRole parentRole = parentUserRole.getRole();

                // Only inherit if the role is marked as inheritable
                if (parentRole.isInheritable()) {
                    Set<EnhancedPermission> rolePermissions = parentRole.getAllPermissions();

                    // Filter permissions that are applicable to child organizations
                    Set<EnhancedPermission> applicablePermissions = rolePermissions.stream()
                        .filter(this::isInheritableToChildOrganizations)
                        .collect(Collectors.toSet());

                    inheritedPermissions.addAll(applicablePermissions);
                }
            }
        }

        return inheritedPermissions;
    }

    /**
     * Roles are now organization-specific with no cross-organizational hierarchy
     * Role hierarchy is determined by organizational hierarchy
     */

    /**
     * Get roles in the same organization hierarchy
     */
    @Transactional(readOnly = true)
    public Set<EnhancedRole> getRolesInOrganizationHierarchy(Organization organization) {
        Set<EnhancedRole> roles = new HashSet<>();

        // Get roles from current organization
        List<EnhancedRole> orgRoles = roleRepository.findByOrganizationAndStatus(organization, "ACTIVE");
        roles.addAll(orgRoles);

        // Get roles from parent organizations
        List<Organization> parentOrganizations = organizationHierarchyService.getParentOrganizations(organization);
        for (Organization parentOrg : parentOrganizations) {
            List<EnhancedRole> parentRoles = roleRepository.findByOrganizationAndStatus(parentOrg, "ACTIVE");
            roles.addAll(parentRoles);
        }

        // Get roles from child organizations
        Set<Organization> childOrganizations = organizationHierarchyService.getDescendantOrganizations(organization);
        for (Organization childOrg : childOrganizations) {
            List<EnhancedRole> childRoles = roleRepository.findByOrganizationAndStatus(childOrg, "ACTIVE");
            roles.addAll(childRoles);
        }

        return roles;
    }

    /**
     * Check if a user has a specific role (including organizational inheritance)
     */
    @Transactional(readOnly = true)
    public boolean hasRole(User user, String roleCode, Organization organization) {
        // Check direct role assignment in the organization
        List<EnhancedUserRole> userRoles = userRoleRepository.findActiveRolesByUserAndOrganization(
            user.getId(), organization.getId());

        boolean hasDirectRole = userRoles.stream()
            .anyMatch(ur -> ur.getRole().getCode().equals(roleCode));

        if (hasDirectRole) {
            return true;
        }

        // Check inherited roles from parent organizations only
        List<Organization> parentOrganizations = organizationHierarchyService.getParentOrganizations(organization);

        for (Organization parentOrg : parentOrganizations) {
            List<EnhancedUserRole> parentRoles = userRoleRepository.findActiveRolesByUserAndOrganization(
                user.getId(), parentOrg.getId());

            boolean hasInheritedRole = parentRoles.stream()
                .filter(ur -> ur.getRole().isInheritable())
                .anyMatch(ur -> ur.getRole().getCode().equals(roleCode));

            if (hasInheritedRole) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate role assignment within organizational constraints
     */
    public void validateRoleAssignment(EnhancedRole role, Organization organization) {
        // Role must belong to the same organization or be inheritable from parent organization
        if (!role.getOrganization().getId().equals(organization.getId())) {
            // Check if role is from a parent organization and is inheritable
            List<Organization> parentOrganizations = organizationHierarchyService.getParentOrganizations(organization);
            boolean isFromParentOrg = parentOrganizations.stream()
                .anyMatch(parentOrg -> parentOrg.getId().equals(role.getOrganization().getId()));

            if (!isFromParentOrg || !role.isInheritable()) {
                throw new IllegalArgumentException("Role can only be assigned within its organization or inherited from parent organizations");
            }
        }

        // Validate organization type compatibility
        if (!isOrganizationTypeCompatible(role.getOrganizationType(), organization.getType())) {
            throw new IllegalArgumentException("Role organization type is not compatible with target organization");
        }
    }

    /**
     * Check if role organization type is compatible with target organization
     */
    private boolean isOrganizationTypeCompatible(OrganizationType roleOrgType, OrganizationType targetOrgType) {
        // Exact match is always compatible
        if (roleOrgType.equals(targetOrgType)) {
            return true;
        }

        // Super Smaile roles can be assigned to any organization
        if (roleOrgType == OrganizationType.SUPER_SMAILE) {
            return true;
        }

        // Define specific compatibility rules based on business requirements
        return switch (roleOrgType) {
            case IC -> targetOrgType.name().startsWith("IC_");
            case SMAILE_TPA -> targetOrgType.name().startsWith("SMAILE_");
            default -> false;
        };
    }

    /**
     * Check if a permission is inheritable to child organizations
     */
    private boolean isInheritableToChildOrganizations(EnhancedPermission permission) {
        // Permissions with ORGANIZATION_TREE or CHILD_ORGANIZATIONS scope are inheritable
        return switch (permission.getScope()) {
            case ORGANIZATION_TREE, CHILD_ORGANIZATIONS, GLOBAL -> true;
            default -> false;
        };
    }

    /**
     * Update role hierarchy level based on organization hierarchy
     */
    public void updateRoleHierarchyLevel(EnhancedRole role) {
        role.setHierarchyLevelFromOrganization();
        roleRepository.save(role);

        log.info("Updated hierarchy level for role: {} to level {}", role.getName(), role.getHierarchyLevel());
    }
}
