package com.smaile.health.security.service;

import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
import com.smaile.health.security.audit.SecurityAuditService;
import com.smaile.health.security.audit.SecurityEventType;
import com.smaile.health.security.domain.EnhancedPermission;
import com.smaile.health.security.domain.EnhancedRole;
import com.smaile.health.security.domain.EnhancedUserRole;
import com.smaile.health.security.repository.EnhancedRoleRepository;
import com.smaile.health.security.repository.EnhancedUserRoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for managing role hierarchy and inheritance.
 * Handles role relationships, permission inheritance, and organizational role propagation.
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class RoleHierarchyService {

    private final EnhancedRoleRepository roleRepository;
    private final EnhancedUserRoleRepository userRoleRepository;
    private final SecurityAuditService auditService;
    private final OrganizationHierarchyService organizationHierarchyService;

    /**
     * Get all effective permissions for a user across all organizations
     */
    @Transactional(readOnly = true)
    public Set<EnhancedPermission> getEffectivePermissions(User user) {
        Set<EnhancedPermission> allPermissions = new HashSet<>();
        
        // Get all active user roles
        List<EnhancedUserRole> activeUserRoles = userRoleRepository.findActiveRolesByUser(user.getId());
        
        for (EnhancedUserRole userRole : activeUserRoles) {
            // Get permissions from the role itself
            Set<EnhancedPermission> rolePermissions = userRole.getRole().getAllPermissions();
            allPermissions.addAll(rolePermissions);
            
            // Get inherited permissions from parent organizations
            Set<EnhancedPermission> inheritedPermissions = getInheritedPermissions(
                userRole.getUser(), userRole.getRole(), userRole.getOrganization());
            allPermissions.addAll(inheritedPermissions);
        }
        
        return allPermissions;
    }

    /**
     * Get effective permissions for a user in a specific organization
     */
    @Transactional(readOnly = true)
    public Set<EnhancedPermission> getEffectivePermissions(User user, Organization organization) {
        Set<EnhancedPermission> permissions = new HashSet<>();
        
        // Get user's roles in this organization
        List<EnhancedUserRole> userRoles = userRoleRepository.findActiveRolesByUserAndOrganization(
            user.getId(), organization.getId());
        
        for (EnhancedUserRole userRole : userRoles) {
            permissions.addAll(userRole.getRole().getAllPermissions());
        }
        
        // Add inherited permissions from parent organizations
        permissions.addAll(getInheritedPermissions(user, null, organization));
        
        return permissions;
    }

    /**
     * Get inherited permissions from parent organizations
     */
    @Transactional(readOnly = true)
    public Set<EnhancedPermission> getInheritedPermissions(User user, EnhancedRole role, Organization organization) {
        Set<EnhancedPermission> inheritedPermissions = new HashSet<>();
        
        // Get parent organizations
        List<Organization> parentOrganizations = organizationHierarchyService.getParentOrganizations(organization);
        
        for (Organization parentOrg : parentOrganizations) {
            // Get user's roles in parent organization
            List<EnhancedUserRole> parentRoles = userRoleRepository.findActiveRolesByUserAndOrganization(
                user.getId(), parentOrg.getId());
            
            for (EnhancedUserRole parentUserRole : parentRoles) {
                EnhancedRole parentRole = parentUserRole.getRole();
                
                // Only inherit if the role is marked as inheritable
                if (parentRole.isInheritable()) {
                    Set<EnhancedPermission> rolePermissions = parentRole.getAllPermissions();
                    
                    // Filter permissions that are applicable to child organizations
                    Set<EnhancedPermission> applicablePermissions = rolePermissions.stream()
                        .filter(this::isInheritableToChildOrganizations)
                        .collect(Collectors.toSet());
                    
                    inheritedPermissions.addAll(applicablePermissions);
                }
            }
        }
        
        return inheritedPermissions;
    }

    /**
     * Create a role hierarchy relationship
     */
    public void createRoleHierarchy(EnhancedRole parentRole, EnhancedRole childRole, User createdBy) {
        validateRoleHierarchy(parentRole, childRole);
        
        childRole.setParentRole(parentRole);
        roleRepository.save(childRole);
        
        auditService.logSecurityEvent(
            SecurityEventType.ROLE_UPDATED,
            "Role hierarchy created: " + childRole.getName() + " -> " + parentRole.getName(),
            "ROLE", childRole.getId(), childRole.getName()
        );
        
        log.info("Role hierarchy created: {} is now child of {}", childRole.getName(), parentRole.getName());
    }

    /**
     * Remove role hierarchy relationship
     */
    public void removeRoleHierarchy(EnhancedRole childRole, User modifiedBy) {
        EnhancedRole formerParent = childRole.getParentRole();
        
        childRole.setParentRole(null);
        roleRepository.save(childRole);
        
        if (formerParent != null) {
            auditService.logSecurityEvent(
                SecurityEventType.ROLE_UPDATED,
                "Role hierarchy removed: " + childRole.getName() + " no longer child of " + formerParent.getName(),
                "ROLE", childRole.getId(), childRole.getName()
            );
            
            log.info("Role hierarchy removed: {} is no longer child of {}", 
                    childRole.getName(), formerParent.getName());
        }
    }

    /**
     * Get all roles in hierarchy (including ancestors and descendants)
     */
    @Transactional(readOnly = true)
    public Set<EnhancedRole> getRoleHierarchy(EnhancedRole role) {
        Set<EnhancedRole> hierarchy = new HashSet<>();
        
        // Add ancestors
        hierarchy.addAll(getAncestorRoles(role));
        
        // Add the role itself
        hierarchy.add(role);
        
        // Add descendants
        hierarchy.addAll(getDescendantRoles(role));
        
        return hierarchy;
    }

    /**
     * Get all ancestor roles (parent, grandparent, etc.)
     */
    @Transactional(readOnly = true)
    public Set<EnhancedRole> getAncestorRoles(EnhancedRole role) {
        Set<EnhancedRole> ancestors = new HashSet<>();
        EnhancedRole current = role.getParentRole();
        
        while (current != null) {
            ancestors.add(current);
            current = current.getParentRole();
        }
        
        return ancestors;
    }

    /**
     * Get all descendant roles (children, grandchildren, etc.)
     */
    @Transactional(readOnly = true)
    public Set<EnhancedRole> getDescendantRoles(EnhancedRole role) {
        Set<EnhancedRole> descendants = new HashSet<>();
        
        for (EnhancedRole childRole : role.getChildRoles()) {
            descendants.add(childRole);
            descendants.addAll(getDescendantRoles(childRole));
        }
        
        return descendants;
    }

    /**
     * Check if a user has a specific role (including inherited roles)
     */
    @Transactional(readOnly = true)
    public boolean hasRole(User user, String roleCode, Organization organization) {
        // Check direct role assignment
        List<EnhancedUserRole> userRoles = userRoleRepository.findActiveRolesByUserAndOrganization(
            user.getId(), organization.getId());
        
        boolean hasDirectRole = userRoles.stream()
            .anyMatch(ur -> ur.getRole().getCode().equals(roleCode));
        
        if (hasDirectRole) {
            return true;
        }
        
        // Check inherited roles from parent organizations
        List<Organization> parentOrganizations = organizationHierarchyService.getParentOrganizations(organization);
        
        for (Organization parentOrg : parentOrganizations) {
            List<EnhancedUserRole> parentRoles = userRoleRepository.findActiveRolesByUserAndOrganization(
                user.getId(), parentOrg.getId());
            
            boolean hasInheritedRole = parentRoles.stream()
                .filter(ur -> ur.getRole().isInheritable())
                .anyMatch(ur -> ur.getRole().getCode().equals(roleCode) || 
                              hasRoleInHierarchy(ur.getRole(), roleCode));
            
            if (hasInheritedRole) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Check if a role or its ancestors have a specific role code
     */
    private boolean hasRoleInHierarchy(EnhancedRole role, String roleCode) {
        if (role.getCode().equals(roleCode)) {
            return true;
        }
        
        return getAncestorRoles(role).stream()
            .anyMatch(ancestorRole -> ancestorRole.getCode().equals(roleCode));
    }

    /**
     * Validate role hierarchy to prevent cycles and invalid relationships
     */
    private void validateRoleHierarchy(EnhancedRole parentRole, EnhancedRole childRole) {
        if (parentRole.equals(childRole)) {
            throw new IllegalArgumentException("A role cannot be its own parent");
        }
        
        // Check for cycles
        if (wouldCreateCycle(parentRole, childRole)) {
            throw new IllegalArgumentException("Role hierarchy would create a cycle");
        }
        
        // Check organization type compatibility
        if (!areOrganizationTypesCompatible(parentRole, childRole)) {
            throw new IllegalArgumentException("Role organization types are not compatible for hierarchy");
        }
    }

    /**
     * Check if creating a hierarchy would create a cycle
     */
    private boolean wouldCreateCycle(EnhancedRole parentRole, EnhancedRole childRole) {
        Set<EnhancedRole> childAncestors = getAncestorRoles(childRole);
        return childAncestors.contains(parentRole);
    }

    /**
     * Check if organization types are compatible for role hierarchy
     */
    private boolean areOrganizationTypesCompatible(EnhancedRole parentRole, EnhancedRole childRole) {
        // Same organization type is always compatible
        if (parentRole.getOrganizationType().equals(childRole.getOrganizationType())) {
            return true;
        }
        
        // Define compatible organization type hierarchies
        // This can be customized based on business rules
        return switch (parentRole.getOrganizationType()) {
            case SUPER_SMAILE -> true; // Super admin can be parent of any role
            case IC -> childRole.getOrganizationType().name().startsWith("IC_");
            case SMAILE_TPA -> childRole.getOrganizationType().name().startsWith("SMAILE_");
            default -> false;
        };
    }

    /**
     * Check if a permission is inheritable to child organizations
     */
    private boolean isInheritableToChildOrganizations(EnhancedPermission permission) {
        // Permissions with ORGANIZATION_TREE or CHILD_ORGANIZATIONS scope are inheritable
        return switch (permission.getScope()) {
            case ORGANIZATION_TREE, CHILD_ORGANIZATIONS, GLOBAL -> true;
            default -> false;
        };
    }

    /**
     * Propagate role changes to child organizations
     */
    public void propagateRoleChanges(EnhancedRole role, User modifiedBy) {
        // This method can be used to propagate role permission changes
        // to users in child organizations who have inherited this role
        
        auditService.logSecurityEvent(
            SecurityEventType.ROLE_UPDATED,
            "Role changes propagated for role: " + role.getName(),
            "ROLE", role.getId(), role.getName()
        );
        
        log.info("Role changes propagated for role: {}", role.getName());
    }
}
