package com.smaile.health.security.repository;

import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleScope;
import com.smaile.health.domain.Organization;
import com.smaile.health.security.domain.EnhancedRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for EnhancedRole entities.
 * Provides methods to query roles with various filters and hierarchy operations.
 */
@Repository
public interface EnhancedRoleRepository extends JpaRepository<EnhancedRole, UUID>, 
                                               JpaSpecificationExecutor<EnhancedRole> {

    /**
     * Find role by code and organization type
     */
    Optional<EnhancedRole> findByCodeAndOrganizationType(String code, OrganizationType organizationType);

    /**
     * Find roles by organization type
     */
    List<EnhancedRole> findByOrganizationTypeAndStatus(OrganizationType organizationType, String status);

    /**
     * Find roles by organization
     */
    List<EnhancedRole> findByOrganizationAndStatus(Organization organization, String status);

    /**
     * Find roles by scope
     */
    List<EnhancedRole> findByScopeAndStatus(RoleScope scope, String status);

    /**
     * Find system roles
     */
    List<EnhancedRole> findBySystemRoleTrueAndStatus(String status);

    /**
     * Find inheritable roles
     */
    List<EnhancedRole> findByInheritableTrueAndStatus(String status);

    /**
     * Find roles by hierarchy level
     */
    List<EnhancedRole> findByHierarchyLevelAndStatus(Integer hierarchyLevel, String status);

    /**
     * Find root roles (no parent)
     */
    List<EnhancedRole> findByParentRoleIsNullAndStatus(String status);

    /**
     * Find child roles of a parent role
     */
    List<EnhancedRole> findByParentRoleAndStatus(EnhancedRole parentRole, String status);

    /**
     * Find roles with specific permission
     */
    @Query("SELECT DISTINCT r FROM EnhancedRole r JOIN r.permissions p " +
           "WHERE p.resource = :resource AND p.action = :action AND r.status = :status")
    List<EnhancedRole> findRolesWithPermission(@Param("resource") String resource,
                                              @Param("action") String action,
                                              @Param("status") String status);

    /**
     * Find roles that can be assigned to a specific organization type
     */
    @Query("SELECT r FROM EnhancedRole r WHERE r.organizationType = :orgType " +
           "OR r.organizationType = 'SUPER_SMAILE' AND r.status = :status")
    List<EnhancedRole> findAssignableRoles(@Param("orgType") OrganizationType organizationType,
                                          @Param("status") String status);

    /**
     * Find roles by name pattern
     */
    @Query("SELECT r FROM EnhancedRole r WHERE LOWER(r.name) LIKE LOWER(CONCAT('%', :namePattern, '%')) " +
           "AND r.status = :status")
    Page<EnhancedRole> findByNameContainingIgnoreCase(@Param("namePattern") String namePattern,
                                                      @Param("status") String status,
                                                      Pageable pageable);

    /**
     * Count roles by organization type
     */
    long countByOrganizationTypeAndStatus(OrganizationType organizationType, String status);

    /**
     * Count roles by scope
     */
    long countByScopeAndStatus(RoleScope scope, String status);

    /**
     * Find roles with hierarchy depth greater than specified level
     */
    @Query("SELECT r FROM EnhancedRole r WHERE r.hierarchyLevel > :level AND r.status = :status")
    List<EnhancedRole> findRolesWithDepthGreaterThan(@Param("level") Integer level,
                                                     @Param("status") String status);

    /**
     * Find all ancestor roles for a given role
     */
    @Query(value = """
        WITH RECURSIVE role_hierarchy AS (
            SELECT id, parent_role_id, name, code, hierarchy_level, 0 as depth
            FROM enhanced_roles 
            WHERE id = :roleId
            
            UNION ALL
            
            SELECT r.id, r.parent_role_id, r.name, r.code, r.hierarchy_level, rh.depth + 1
            FROM enhanced_roles r
            INNER JOIN role_hierarchy rh ON r.id = rh.parent_role_id
            WHERE rh.depth < 10
        )
        SELECT * FROM role_hierarchy WHERE depth > 0
        """, nativeQuery = true)
    List<Object[]> findAncestorRoles(@Param("roleId") UUID roleId);

    /**
     * Find all descendant roles for a given role
     */
    @Query(value = """
        WITH RECURSIVE role_hierarchy AS (
            SELECT id, parent_role_id, name, code, hierarchy_level, 0 as depth
            FROM enhanced_roles 
            WHERE id = :roleId
            
            UNION ALL
            
            SELECT r.id, r.parent_role_id, r.name, r.code, r.hierarchy_level, rh.depth + 1
            FROM enhanced_roles r
            INNER JOIN role_hierarchy rh ON r.parent_role_id = rh.id
            WHERE rh.depth < 10
        )
        SELECT * FROM role_hierarchy WHERE depth > 0
        """, nativeQuery = true)
    List<Object[]> findDescendantRoles(@Param("roleId") UUID roleId);

    /**
     * Check if role hierarchy would create a cycle
     */
    @Query(value = """
        WITH RECURSIVE role_hierarchy AS (
            SELECT id, parent_role_id, 0 as depth
            FROM enhanced_roles 
            WHERE id = :childRoleId
            
            UNION ALL
            
            SELECT r.id, r.parent_role_id, rh.depth + 1
            FROM enhanced_roles r
            INNER JOIN role_hierarchy rh ON r.id = rh.parent_role_id
            WHERE rh.depth < 10
        )
        SELECT COUNT(*) > 0 FROM role_hierarchy WHERE id = :parentRoleId
        """, nativeQuery = true)
    boolean wouldCreateCycle(@Param("parentRoleId") UUID parentRoleId, @Param("childRoleId") UUID childRoleId);

    /**
     * Find roles that have been modified recently
     */
    @Query("SELECT r FROM EnhancedRole r WHERE r.lastUpdated >= :since AND r.status = :status " +
           "ORDER BY r.lastUpdated DESC")
    List<EnhancedRole> findRecentlyModifiedRoles(@Param("since") java.time.OffsetDateTime since,
                                                 @Param("status") String status);

    /**
     * Find roles with specific permission count
     */
    @Query("SELECT r FROM EnhancedRole r WHERE SIZE(r.permissions) >= :minPermissions " +
           "AND r.status = :status ORDER BY SIZE(r.permissions) DESC")
    List<EnhancedRole> findRolesWithMinimumPermissions(@Param("minPermissions") int minPermissions,
                                                       @Param("status") String status);

    /**
     * Find orphaned roles (roles that should have a parent but don't)
     */
    @Query("SELECT r FROM EnhancedRole r WHERE r.parentRole IS NULL " +
           "AND r.hierarchyLevel > 0 AND r.status = :status")
    List<EnhancedRole> findOrphanedRoles(@Param("status") String status);

    /**
     * Get role statistics by organization type
     */
    @Query("SELECT r.organizationType, COUNT(r), AVG(SIZE(r.permissions)) " +
           "FROM EnhancedRole r WHERE r.status = :status " +
           "GROUP BY r.organizationType")
    List<Object[]> getRoleStatisticsByOrganizationType(@Param("status") String status);

    /**
     * Find roles that can be inherited by child organizations
     */
    @Query("SELECT r FROM EnhancedRole r WHERE r.inheritable = true " +
           "AND r.organizationType = :orgType AND r.status = :status")
    List<EnhancedRole> findInheritableRolesByOrganizationType(@Param("orgType") OrganizationType organizationType,
                                                             @Param("status") String status);
}
