package com.smaile.health.security.domain;

import com.smaile.health.domain.BaseEntity;
import com.smaile.health.security.model.SecurityAction;
import com.smaile.health.security.model.SecurityResource;
import com.smaile.health.security.model.SecurityScope;
import jakarta.persistence.*;
import lombok.*;

import java.util.Set;
import java.util.UUID;

/**
 * Enhanced Permission entity with structured resource-action-scope model.
 * Replaces the basic Permission entity with enterprise-level permission management.
 */
@Entity
@Table(name = "enhanced_permissions", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"resource", "action", "scope", "condition"}))
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = {"roles"})
public class EnhancedPermission extends BaseEntity {

    @Id
    @Column(nullable = false, updatable = false)
    private UUID id;

    @Column(nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private SecurityResource resource;

    @Column(nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private SecurityAction action;

    @Column(nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private SecurityScope scope;

    @Column(length = 255)
    private String condition;

    @Column(nullable = false, length = 255)
    private String name;

    @Column(columnDefinition = "text")
    private String description;

    @Column(nullable = false)
    private boolean systemPermission = false;

    @Column(nullable = false, length = 20)
    private String status = "ACTIVE";

    @ManyToMany(mappedBy = "permissions", fetch = FetchType.LAZY)
    private Set<EnhancedRole> roles;

    /**
     * Get the permission string representation
     */
    public String getPermissionString() {
        StringBuilder sb = new StringBuilder();
        sb.append(resource.getCode())
          .append(":")
          .append(action.getCode())
          .append(":")
          .append(scope.getCode());
        
        if (condition != null && !condition.trim().isEmpty()) {
            sb.append(":").append(condition);
        }
        
        return sb.toString();
    }

    /**
     * Check if this permission implies another permission
     */
    public boolean implies(EnhancedPermission other) {
        if (other == null) {
            return false;
        }

        // Resource must match exactly
        if (!this.resource.equals(other.resource)) {
            return false;
        }

        // Action must match exactly or this must be MANAGE (which implies all actions)
        if (!this.action.equals(other.action) && this.action != SecurityAction.MANAGE) {
            return false;
        }

        // Scope must include the other scope
        if (!this.scope.includes(other.scope)) {
            return false;
        }

        // Handle conditions
        if (this.condition != null && other.condition != null) {
            return this.condition.equals(other.condition);
        } else if (this.condition != null && other.condition == null) {
            return false;
        }

        return true;
    }

    @PrePersist
    protected void onCreate() {
        if (id == null) {
            id = UUID.randomUUID();
        }
        if (name == null || name.trim().isEmpty()) {
            generateName();
        }
    }

    private void generateName() {
        StringBuilder nameBuilder = new StringBuilder();
        nameBuilder.append(resource.getDescription())
                   .append(" - ")
                   .append(action.getDescription())
                   .append(" (")
                   .append(scope.getDescription())
                   .append(")");
        this.name = nameBuilder.toString();
    }
}
