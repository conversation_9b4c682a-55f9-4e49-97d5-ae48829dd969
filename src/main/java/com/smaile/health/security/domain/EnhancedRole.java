package com.smaile.health.security.domain;

import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleScope;
import com.smaile.health.domain.BaseEntity;
import com.smaile.health.domain.Organization;
import jakarta.persistence.*;
import lombok.*;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * Enhanced Role entity with hierarchical structure and improved permission management.
 * Supports role inheritance and organization-specific role definitions.
 */
@Entity
@Table(name = "enhanced_roles", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"code", "organization_type"}))
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = {"permissions", "parentRole", "childRoles", "userRoles"})
public class EnhancedRole extends BaseEntity {

    @Id
    @Column(nullable = false, updatable = false)
    private UUID id;

    @Column(nullable = false, length = 100)
    private String name;

    @Column(nullable = false, length = 50)
    private String code;

    @Column(columnDefinition = "text")
    private String description;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private OrganizationType organizationType;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private RoleScope scope;

    @Column(nullable = false)
    private Integer hierarchyLevel = 0;

    @Column(nullable = false)
    private boolean systemRole = false;

    @Column(nullable = false)
    private boolean inheritable = true;

    @Column(nullable = false, length = 20)
    private String status = "ACTIVE";

    // Organization-specific role definition - no cross-organizational hierarchy
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "organization_id", nullable = false)
    private Organization organization;

    // Permissions
    @ManyToMany(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
        name = "enhanced_roles_permissions",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    private Set<EnhancedPermission> permissions = new HashSet<>();

    // User Assignments
    @OneToMany(mappedBy = "role", fetch = FetchType.LAZY)
    private Set<EnhancedUserRole> userRoles = new HashSet<>();

    /**
     * Get all permissions for this role (no role hierarchy inheritance)
     */
    public Set<EnhancedPermission> getAllPermissions() {
        return new HashSet<>(this.permissions);
    }

    /**
     * Add permission to this role
     */
    public void addPermission(EnhancedPermission permission) {
        if (permission != null) {
            this.permissions.add(permission);
            if (permission.getRoles() == null) {
                permission.setRoles(new HashSet<>());
            }
            permission.getRoles().add(this);
        }
    }

    /**
     * Remove permission from this role
     */
    public void removePermission(EnhancedPermission permission) {
        if (permission != null) {
            this.permissions.remove(permission);
            if (permission.getRoles() != null) {
                permission.getRoles().remove(this);
            }
        }
    }

    /**
     * Set hierarchy level based on organization hierarchy
     */
    public void setHierarchyLevelFromOrganization() {
        if (organization != null) {
            this.hierarchyLevel = calculateOrganizationDepth(organization);
        } else {
            this.hierarchyLevel = 0;
        }
    }

    /**
     * Calculate organization depth for hierarchy level
     */
    private int calculateOrganizationDepth(Organization org) {
        int depth = 0;
        Organization current = org.getParent();
        while (current != null) {
            depth++;
            current = current.getParent();
        }
        return depth;
    }

    /**
     * Check if this role has a specific permission (direct permissions only)
     */
    public boolean hasPermission(EnhancedPermission permission) {
        return permissions.stream()
                .anyMatch(p -> p.implies(permission));
    }

    /**
     * Check if this role has a permission by string (direct permissions only)
     */
    public boolean hasPermission(String permissionString) {
        return permissions.stream()
                .anyMatch(p -> p.getPermissionString().equals(permissionString));
    }

    @PrePersist
    protected void onCreate() {
        if (id == null) {
            id = UUID.randomUUID();
        }
    }
}
