package com.smaile.health.security.domain;

import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleScope;
import com.smaile.health.domain.BaseEntity;
import jakarta.persistence.*;
import lombok.*;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * Enhanced Role entity with hierarchical structure and improved permission management.
 * Supports role inheritance and organization-specific role definitions.
 */
@Entity
@Table(name = "enhanced_roles", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"code", "organization_type"}))
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = {"permissions", "parentRole", "childRoles", "userRoles"})
public class EnhancedRole extends BaseEntity {

    @Id
    @Column(nullable = false, updatable = false)
    private UUID id;

    @Column(nullable = false, length = 100)
    private String name;

    @Column(nullable = false, length = 50)
    private String code;

    @Column(columnDefinition = "text")
    private String description;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private OrganizationType organizationType;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private RoleScope scope;

    @Column(nullable = false)
    private Integer hierarchyLevel = 0;

    @Column(nullable = false)
    private boolean systemRole = false;

    @Column(nullable = false)
    private boolean inheritable = true;

    @Column(nullable = false, length = 20)
    private String status = "ACTIVE";

    // Role Hierarchy
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_role_id")
    private EnhancedRole parentRole;

    @OneToMany(mappedBy = "parentRole", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private Set<EnhancedRole> childRoles = new HashSet<>();

    // Permissions
    @ManyToMany(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
        name = "enhanced_roles_permissions",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    private Set<EnhancedPermission> permissions = new HashSet<>();

    // User Assignments
    @OneToMany(mappedBy = "role", fetch = FetchType.LAZY)
    private Set<EnhancedUserRole> userRoles = new HashSet<>();

    /**
     * Get all permissions including inherited ones from parent roles
     */
    public Set<EnhancedPermission> getAllPermissions() {
        Set<EnhancedPermission> allPermissions = new HashSet<>(this.permissions);
        
        // Add inherited permissions from parent role
        if (parentRole != null && inheritable) {
            allPermissions.addAll(parentRole.getAllPermissions());
        }
        
        return allPermissions;
    }

    /**
     * Add permission to this role
     */
    public void addPermission(EnhancedPermission permission) {
        if (permission != null) {
            this.permissions.add(permission);
            if (permission.getRoles() == null) {
                permission.setRoles(new HashSet<>());
            }
            permission.getRoles().add(this);
        }
    }

    /**
     * Remove permission from this role
     */
    public void removePermission(EnhancedPermission permission) {
        if (permission != null) {
            this.permissions.remove(permission);
            if (permission.getRoles() != null) {
                permission.getRoles().remove(this);
            }
        }
    }

    /**
     * Set parent role and update hierarchy level
     */
    public void setParentRole(EnhancedRole parentRole) {
        // Remove from old parent
        if (this.parentRole != null) {
            this.parentRole.getChildRoles().remove(this);
        }
        
        this.parentRole = parentRole;
        
        // Add to new parent and update hierarchy level
        if (parentRole != null) {
            parentRole.getChildRoles().add(this);
            this.hierarchyLevel = parentRole.getHierarchyLevel() + 1;
            
            // Update hierarchy levels of all child roles
            updateChildHierarchyLevels();
        } else {
            this.hierarchyLevel = 0;
        }
    }

    /**
     * Update hierarchy levels of all child roles recursively
     */
    private void updateChildHierarchyLevels() {
        for (EnhancedRole childRole : childRoles) {
            childRole.hierarchyLevel = this.hierarchyLevel + 1;
            childRole.updateChildHierarchyLevels();
        }
    }

    /**
     * Check if this role has a specific permission (including inherited)
     */
    public boolean hasPermission(EnhancedPermission permission) {
        return getAllPermissions().stream()
                .anyMatch(p -> p.implies(permission));
    }

    /**
     * Check if this role has a permission by string
     */
    public boolean hasPermission(String permissionString) {
        return getAllPermissions().stream()
                .anyMatch(p -> p.getPermissionString().equals(permissionString));
    }

    @PrePersist
    protected void onCreate() {
        if (id == null) {
            id = UUID.randomUUID();
        }
    }
}
