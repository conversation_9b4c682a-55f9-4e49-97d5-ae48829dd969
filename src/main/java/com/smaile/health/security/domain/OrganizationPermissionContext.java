package com.smaile.health.security.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;
import java.util.UUID;

/**
 * Represents the permission context for a user within a specific organization.
 * Contains all permissions available to the user in that organization.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationPermissionContext {
    
    private UUID organizationId;
    private String organizationName;
    private Set<String> permissions;
    private Set<String> roles;
    private boolean isInherited; // true if permissions are inherited from parent organization
    
    /**
     * Check if user has a specific permission in this organization
     */
    public boolean hasPermission(String permission) {
        return permissions != null && permissions.contains(permission);
    }
    
    /**
     * Check if user has a specific role in this organization
     */
    public boolean hasRole(String role) {
        return roles != null && roles.contains(role);
    }
    
    /**
     * Check if user has any of the specified permissions
     */
    public boolean hasAnyPermission(Set<String> requiredPermissions) {
        if (permissions == null || requiredPermissions == null) {
            return false;
        }
        return requiredPermissions.stream().anyMatch(permissions::contains);
    }
    
    /**
     * Check if user has all of the specified permissions
     */
    public boolean hasAllPermissions(Set<String> requiredPermissions) {
        if (permissions == null || requiredPermissions == null) {
            return false;
        }
        return permissions.containsAll(requiredPermissions);
    }
}
