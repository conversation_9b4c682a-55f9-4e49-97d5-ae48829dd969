package com.smaile.health.security.domain;

import com.smaile.health.domain.BaseEntity;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
import jakarta.persistence.*;
import lombok.*;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * Enhanced User-Role assignment entity with temporal validity and audit trail.
 * Supports time-bound role assignments and organization-specific role grants.
 */
@Entity
@Table(name = "enhanced_user_roles",
       uniqueConstraints = @UniqueConstraint(columnNames = {"user_id", "role_id", "organization_id"}))
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = {"user", "role", "organization", "assignedByUser"})
public class EnhancedUserRole extends BaseEntity {

    @Id
    @Column(nullable = false, updatable = false)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "role_id", nullable = false)
    private EnhancedRole role;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "organization_id", nullable = false)
    private Organization organization;

    @Column(nullable = false, length = 20)
    private String status = "ACTIVE";

    // Temporal validity
    @Column(nullable = false)
    private OffsetDateTime validFrom;

    @Column
    private OffsetDateTime validTo;

    // Assignment audit
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assigned_by_user_id")
    private User assignedByUser;

    @Column(length = 500)
    private String assignmentReason;

    @Column(length = 500)
    private String assignmentNotes;

    // Revocation audit
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "revoked_by_user_id")
    private User revokedByUser;

    @Column
    private OffsetDateTime revokedAt;

    @Column(length = 500)
    private String revocationReason;

    /**
     * Check if this role assignment is currently active
     */
    public boolean isActive() {
        OffsetDateTime now = OffsetDateTime.now();
        return "ACTIVE".equals(status) &&
               (validFrom == null || !validFrom.isAfter(now)) &&
               (validTo == null || !validTo.isBefore(now)) &&
               revokedAt == null;
    }

    /**
     * Check if this role assignment is valid at a specific time
     */
    public boolean isValidAt(OffsetDateTime dateTime) {
        return "ACTIVE".equals(status) &&
               (validFrom == null || !validFrom.isAfter(dateTime)) &&
               (validTo == null || !validTo.isBefore(dateTime)) &&
               (revokedAt == null || !revokedAt.isBefore(dateTime));
    }

    /**
     * Revoke this role assignment
     */
    public void revoke(User revokedBy, String reason) {
        this.status = "REVOKED";
        this.revokedAt = OffsetDateTime.now();
        this.revokedByUser = revokedBy;
        this.revocationReason = reason;
    }

    /**
     * Activate this role assignment
     */
    public void activate() {
        this.status = "ACTIVE";
        this.revokedAt = null;
        this.revokedByUser = null;
        this.revocationReason = null;
    }

    /**
     * Set expiration date for this role assignment
     */
    public void setExpiration(OffsetDateTime expirationDate) {
        this.validTo = expirationDate;
    }

    /**
     * Extend the validity of this role assignment
     */
    public void extend(OffsetDateTime newValidTo, User extendedBy, String reason) {
        this.validTo = newValidTo;
        this.assignmentNotes = (this.assignmentNotes != null ? this.assignmentNotes + "; " : "") +
                              "Extended by " + extendedBy.getFullName() + " on " + OffsetDateTime.now() + 
                              (reason != null ? " - " + reason : "");
    }

    @PrePersist
    protected void onCreate() {
        if (id == null) {
            id = UUID.randomUUID();
        }
        if (validFrom == null) {
            validFrom = OffsetDateTime.now();
        }
    }
}
