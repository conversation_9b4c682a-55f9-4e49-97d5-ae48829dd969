package com.smaile.health.security.audit;

import com.smaile.health.domain.BaseEntity;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.OffsetDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * Entity for storing security audit events.
 * Provides comprehensive audit trail for all security-related activities.
 */
@Entity
@Table(name = "security_audit_events", indexes = {
    @Index(name = "idx_security_audit_event_type", columnList = "event_type"),
    @Index(name = "idx_security_audit_user_id", columnList = "user_id"),
    @Index(name = "idx_security_audit_organization_id", columnList = "organization_id"),
    @Index(name = "idx_security_audit_timestamp", columnList = "event_timestamp"),
    @Index(name = "idx_security_audit_severity", columnList = "severity"),
    @Index(name = "idx_security_audit_resource", columnList = "resource_type, resource_id")
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = {"user", "organization"})
public class SecurityAuditEvent extends BaseEntity {

    @Id
    @Column(nullable = false, updatable = false)
    private UUID id;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private SecurityEventType eventType;

    @Column(nullable = false)
    private OffsetDateTime eventTimestamp;

    @Column(nullable = false, length = 10)
    private String severity;

    @Column(nullable = false, length = 500)
    private String description;

    // User context
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;

    @Column(name = "user_email", length = 255)
    private String userEmail;

    @Column(name = "user_name", length = 255)
    private String userName;

    // Organization context
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id")
    private Organization organization;

    @Column(name = "organization_name", length = 255)
    private String organizationName;

    // Resource context
    @Column(name = "resource_type", length = 100)
    private String resourceType;

    @Column(name = "resource_id")
    private UUID resourceId;

    @Column(name = "resource_name", length = 255)
    private String resourceName;

    // Request context
    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    @Column(name = "user_agent", length = 500)
    private String userAgent;

    @Column(name = "session_id", length = 255)
    private String sessionId;

    @Column(name = "request_id", length = 255)
    private String requestId;

    // Event details
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "event_details", columnDefinition = "jsonb")
    private Map<String, Object> eventDetails;

    // Before/After state for change events
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "before_state", columnDefinition = "jsonb")
    private Map<String, Object> beforeState;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "after_state", columnDefinition = "jsonb")
    private Map<String, Object> afterState;

    // Success/Failure information
    @Column(nullable = false)
    private boolean success = true;

    @Column(name = "error_message", length = 1000)
    private String errorMessage;

    @Column(name = "error_code", length = 50)
    private String errorCode;

    // Processing information
    @Column(name = "processing_time_ms")
    private Long processingTimeMs;

    @Column(name = "correlation_id", length = 255)
    private String correlationId;

    // Additional metadata
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "metadata", columnDefinition = "jsonb")
    private Map<String, Object> metadata;

    @PrePersist
    protected void onCreate() {
        if (id == null) {
            id = UUID.randomUUID();
        }
        if (eventTimestamp == null) {
            eventTimestamp = OffsetDateTime.now();
        }
        if (severity == null && eventType != null) {
            severity = eventType.getSeverity();
        }
    }

    /**
     * Create a builder with common fields pre-populated
     */
    public static SecurityAuditEventBuilder builder(SecurityEventType eventType) {
        return SecurityAuditEvent.builder()
                .eventType(eventType)
                .eventTimestamp(OffsetDateTime.now())
                .severity(eventType.getSeverity())
                .description(eventType.getDescription());
    }

    /**
     * Check if this event indicates a security concern
     */
    public boolean isSecurityConcern() {
        return "ERROR".equals(severity) || "WARN".equals(severity) ||
               eventType.getCode().contains("FAILED") ||
               eventType.getCode().contains("DENIED") ||
               eventType.getCode().contains("SUSPICIOUS") ||
               eventType.getCode().contains("UNAUTHORIZED");
    }

    /**
     * Get a summary of this audit event
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("[").append(severity).append("] ");
        summary.append(eventType.getCode()).append(" - ");
        summary.append(description);
        
        if (userName != null) {
            summary.append(" (User: ").append(userName).append(")");
        }
        
        if (organizationName != null) {
            summary.append(" (Org: ").append(organizationName).append(")");
        }
        
        if (resourceType != null && resourceName != null) {
            summary.append(" (Resource: ").append(resourceType).append("/").append(resourceName).append(")");
        }
        
        return summary.toString();
    }
}
