package com.smaile.health.security.context;

import com.smaile.health.domain.Organization;
import com.smaile.health.domain.Permission;
import com.smaile.health.domain.Role;
import com.smaile.health.domain.User;
import com.smaile.health.domain.UserRole;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.UserRoleRepository;
import com.smaile.health.security.domain.OrganizationPermissionContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service responsible for building security context with permissions for each organization
 * after successful authentication.
 */
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
@Slf4j
public class SecurityContextBuilder {

    private final UserRoleRepository userRoleRepository;
    private final OrganizationRepository organizationRepository;

    /**
     * Build organization-specific permission contexts for a user
     */
    public Map<UUID, OrganizationPermissionContext> buildOrganizationPermissionContexts(User user) {
        log.debug("Building permission contexts for user: {}", user.getEmail());
        
        Map<UUID, OrganizationPermissionContext> contexts = new HashMap<>();
        
        // Get all active user roles
        List<UserRole> activeUserRoles = getActiveUserRoles(user);
        
        // Group roles by organization
        Map<UUID, List<UserRole>> rolesByOrganization = activeUserRoles.stream()
            .collect(Collectors.groupingBy(ur -> ur.getUserOrganization().getOrganization().getId()));
        
        // Build context for each organization where user has direct roles
        for (Map.Entry<UUID, List<UserRole>> entry : rolesByOrganization.entrySet()) {
            UUID organizationId = entry.getKey();
            List<UserRole> orgRoles = entry.getValue();
            
            Organization organization = orgRoles.get(0).getUserOrganization().getOrganization();
            OrganizationPermissionContext context = buildDirectPermissionContext(organization, orgRoles);
            contexts.put(organizationId, context);
        }
        
        // Add inherited permissions from parent organizations
        addInheritedPermissions(user, contexts);
        
        log.debug("Built {} permission contexts for user: {}", contexts.size(), user.getEmail());
        return contexts;
    }

    /**
     * Build permission context for direct role assignments in an organization
     */
    private OrganizationPermissionContext buildDirectPermissionContext(Organization organization, List<UserRole> userRoles) {
        Set<String> permissions = new HashSet<>();
        Set<String> roles = new HashSet<>();
        
        for (UserRole userRole : userRoles) {
            Role role = userRole.getRole();
            roles.add(role.getCode());
            
            // Add all permissions from this role
            if (role.getPermissions() != null) {
                Set<String> rolePermissions = role.getPermissions().stream()
                    .map(this::formatPermission)
                    .collect(Collectors.toSet());
                permissions.addAll(rolePermissions);
            }
        }
        
        return OrganizationPermissionContext.builder()
            .organizationId(organization.getId())
            .organizationName(organization.getName())
            .permissions(permissions)
            .roles(roles)
            .isInherited(false)
            .build();
    }

    /**
     * Add inherited permissions from parent organizations
     */
    private void addInheritedPermissions(User user, Map<UUID, OrganizationPermissionContext> contexts) {
        // For each organization context, check parent organizations for inheritable permissions
        Map<UUID, OrganizationPermissionContext> inheritedContexts = new HashMap<>();
        
        for (OrganizationPermissionContext context : contexts.values()) {
            Organization organization = organizationRepository.findById(context.getOrganizationId()).orElse(null);
            if (organization != null) {
                addInheritedPermissionsFromParents(user, organization, inheritedContexts, contexts);
            }
        }
        
        // Add inherited contexts to main contexts
        contexts.putAll(inheritedContexts);
    }

    /**
     * Recursively add inherited permissions from parent organizations
     */
    private void addInheritedPermissionsFromParents(User user, Organization organization, 
                                                   Map<UUID, OrganizationPermissionContext> inheritedContexts,
                                                   Map<UUID, OrganizationPermissionContext> directContexts) {
        
        Organization parentOrg = organization.getParent();
        if (parentOrg == null) {
            return;
        }
        
        // Check if user has roles in parent organization
        List<UserRole> parentRoles = getActiveUserRolesInOrganization(user, parentOrg);
        
        if (!parentRoles.isEmpty()) {
            // Get inheritable permissions from parent organization
            Set<String> inheritablePermissions = new HashSet<>();
            Set<String> inheritableRoles = new HashSet<>();
            
            for (UserRole userRole : parentRoles) {
                Role role = userRole.getRole();
                
                // Only inherit if role is marked as inheritable (assuming this field exists)
                if (isRoleInheritable(role)) {
                    inheritableRoles.add(role.getCode());
                    
                    if (role.getPermissions() != null) {
                        Set<String> rolePermissions = role.getPermissions().stream()
                            .filter(this::isPermissionInheritable)
                            .map(this::formatPermission)
                            .collect(Collectors.toSet());
                        inheritablePermissions.addAll(rolePermissions);
                    }
                }
            }
            
            // Create inherited context for the current organization
            if (!inheritablePermissions.isEmpty() && !directContexts.containsKey(organization.getId())) {
                OrganizationPermissionContext inheritedContext = OrganizationPermissionContext.builder()
                    .organizationId(organization.getId())
                    .organizationName(organization.getName())
                    .permissions(inheritablePermissions)
                    .roles(inheritableRoles)
                    .isInherited(true)
                    .build();
                
                inheritedContexts.put(organization.getId(), inheritedContext);
            }
        }
        
        // Continue up the hierarchy
        addInheritedPermissionsFromParents(user, parentOrg, inheritedContexts, directContexts);
    }

    /**
     * Get active user roles for a user
     */
    private List<UserRole> getActiveUserRoles(User user) {
        OffsetDateTime now = OffsetDateTime.now();
        return userRoleRepository.findByUserOrganization_User(user).stream()
            .filter(ur -> "ACTIVE".equals(ur.getStatus()))
            .filter(ur -> ur.getStartTime() == null || !ur.getStartTime().isAfter(now))
            .filter(ur -> ur.getEndTime() == null || !ur.getEndTime().isBefore(now))
            .collect(Collectors.toList());
    }

    /**
     * Get active user roles in a specific organization
     */
    private List<UserRole> getActiveUserRolesInOrganization(User user, Organization organization) {
        return getActiveUserRoles(user).stream()
            .filter(ur -> ur.getUserOrganization().getOrganization().getId().equals(organization.getId()))
            .collect(Collectors.toList());
    }

    /**
     * Check if a role is inheritable to child organizations
     */
    private boolean isRoleInheritable(Role role) {
        // This would depend on your business logic
        // For now, assume all roles are inheritable unless specifically marked otherwise
        return true;
    }

    /**
     * Check if a permission is inheritable to child organizations
     */
    private boolean isPermissionInheritable(Permission permission) {
        // Check if permission scope allows inheritance
        // This would depend on your permission structure
        return permission.getResource() != null && 
               (permission.getResource().contains("global") || 
                permission.getResource().contains("organization"));
    }

    /**
     * Format permission as string
     */
    private String formatPermission(Permission permission) {
        StringBuilder sb = new StringBuilder();
        
        if (permission.getResource() != null) {
            sb.append(permission.getResource());
        }
        
        if (permission.getSubResource() != null) {
            sb.append(":").append(permission.getSubResource());
        }
        
        if (permission.getAction() != null) {
            sb.append(":").append(permission.getAction());
        }
        
        return sb.toString();
    }

    /**
     * Build simplified permission map for CustomAuthentication
     */
    public Map<UUID, Set<String>> buildPermissionMap(Map<UUID, OrganizationPermissionContext> contexts) {
        return contexts.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().getPermissions()
            ));
    }

    /**
     * Build role set for CustomAuthentication
     */
    public Set<String> buildRoleSet(Map<UUID, OrganizationPermissionContext> contexts) {
        return contexts.values().stream()
            .flatMap(context -> context.getRoles().stream())
            .collect(Collectors.toSet());
    }
}
