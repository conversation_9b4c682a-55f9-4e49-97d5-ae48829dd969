package com.smaile.health.service;

import com.smaile.health.config.CustomAuthentication;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.constants.Status;
import com.smaile.health.model.AboutMeDTO;
import com.smaile.health.model.UserDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface UserService {

    CustomAuthentication getAuthenticationByKeycloakId(String keycloakId);

    AboutMeDTO getAboutMe();

    Page<UserDTO> findActiveUserByOrgId(UUID orgId, Pageable pageable);

    UUID create(UUID orgId, UserDTO userDTO);

    UUID createProfessionalUser(UUID orgId, UserDTO userDTO, String password);

    void update(UUID orgId, UUID id, UserDTO userDTO);

    UserDTO get(UUID id);

    boolean isAvailableToCreate(UserDTO userDto);

    Page<UserDTO> queryUserByCriteria(UUID organizationId, String partialNameOrEmail, RoleEnum roleCode, Status status, Pageable pageable);
}