package com.smaile.health.service.impl;

import com.smaile.health.constants.*;
import com.smaile.health.domain.InsuranceCompany;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.exception.InternalServerException;
import com.smaile.health.mapper.CreateInsuranceCompanyRequestMapper;
import com.smaile.health.mapper.InsuranceCompanyMapper;
import com.smaile.health.mapper.UserMapper;
import com.smaile.health.model.CreateInsuranceCompanyRequestDTO;
import com.smaile.health.model.InsuranceCompanyDTO;
import com.smaile.health.model.UserDTO;
import com.smaile.health.model.InsuranceCompanyGeneralInfoDTO;
import com.smaile.health.repository.InsuranceCompanyRepository;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.specification.InsuranceCompanySpecification;
import com.smaile.health.service.InsuranceCompanyService;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.UserService;
import com.smaile.health.util.SecurityUtils;
import com.smaile.health.util.UUIDv7;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class InsuranceCompanyServiceImpl implements InsuranceCompanyService {

    private final InsuranceCompanyRepository insuranceCompanyRepository;
    private final InsuranceCompanyMapper insuranceCompanyMapper;
    private final CreateInsuranceCompanyRequestMapper createRequestMapper;
    private final I18nService i18nService;
    private final OrganizationRepository organizationRepository;
    private final UserService userService;
    private final UserMapper userMapper;

    @Override
    @Transactional(readOnly = true)
    public Page<InsuranceCompanyDTO> search(String market, String status, String name, Pageable pageable) {
        long startTime = System.currentTimeMillis();
        log.debug("Starting search operation with filters - Market: {}, Status: {}, Name: {}, Page: {}",
                market, status, name, pageable.getPageNumber());

        try {
            Specification<InsuranceCompany> spec = InsuranceCompanySpecification.searchSpecification(market, status, name);
            Page<InsuranceCompany> insuranceCompanies = insuranceCompanyRepository.findAll(spec, pageable);

            long endTime = System.currentTimeMillis();
            log.debug("Search operation completed in {} ms, found {} results",
                    endTime - startTime, insuranceCompanies.getTotalElements());

            return insuranceCompanies.map(this::enrichInsuranceCompanyDTO);
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Search operation failed after {} ms with error: {}", endTime - startTime, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public InsuranceCompanyDTO get(UUID id) {
        log.debug("Fetching insurance company with ID: {}", id);

        try {
            InsuranceCompany insuranceCompany = insuranceCompanyRepository.findByIdAndStatusNot(id, OrganizationStatus.ARCHIVED)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.INSURANCE_COMPANY_NOT_FOUND.getMessageKey())
                    ));

            return enrichInsuranceCompanyDTO(insuranceCompany);
        } catch (NotFoundException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to retrieve insurance company with ID {}: {}", id, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.INSURANCE_COMPANY_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public UUID create(CreateInsuranceCompanyRequestDTO requestDTO) {
        log.debug("Creating new insurance company: {}", requestDTO.getName());
        long startTime = System.currentTimeMillis();

        try {
            // Validate name duplication (excluding archived)
            if (insuranceCompanyRepository.findByNameAndStatusNot(requestDTO.getName(), OrganizationStatus.ARCHIVED).isPresent()) {
                log.warn("Attempted to create insurance company with duplicate name: {}", requestDTO.getName());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.INSURANCE_COMPANY_NAME_DUPLICATE.getMessageKey())
                );
            }

            // Check if admin user information is provided
            if (requestDTO.getAdmin() != null && requestDTO.getAdmin().getEmail() != null && !requestDTO.getAdmin().getEmail().trim().isEmpty()
                    && requestDTO.getAdmin().getFullName() != null && !requestDTO.getAdmin().getFullName().trim().isEmpty()) {
                log.debug("Will create new admin user: {} after insurance company creation", requestDTO.getAdmin().getEmail());
            } else {
                log.debug("No admin user information provided, skipping admin user creation");
            }

            // Get current user's organization to set as parent
            UUID currentUserOrgId = SecurityUtils.getCurrentUserOrganizationId()
                    .orElseThrow(() -> new ValidationException(
                            i18nService.getMessage(ErrorCode.USER_ORGANIZATION_NOT_FOUND.getMessageKey())
                    ));

            Organization parentOrg = organizationRepository.findById(currentUserOrgId)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.ORGANIZATION_NOT_FOUND.getMessageKey())
                    ));

            // Convert request DTO to insurance company DTO
            InsuranceCompanyDTO insuranceCompanyDTO = createRequestMapper.toInsuranceCompanyDTO(requestDTO);

            InsuranceCompany insuranceCompany = insuranceCompanyMapper.toEntity(insuranceCompanyDTO);
            insuranceCompany.setId(UUIDv7.generate());
            insuranceCompany.setType(OrganizationType.IC);
            insuranceCompany.setStatus(OrganizationStatus.ACTIVE);
            insuranceCompany.setParent(parentOrg);

            log.debug("Saving insurance company entity with ID: {}", insuranceCompany.getId());
            InsuranceCompany savedInsuranceCompany = insuranceCompanyRepository.save(insuranceCompany);

            // Create admin user if information is provided
            if (requestDTO.getAdmin() != null && requestDTO.getAdmin().getEmail() != null && !requestDTO.getAdmin().getEmail().trim().isEmpty()
                    && requestDTO.getAdmin().getFullName() != null && !requestDTO.getAdmin().getFullName().trim().isEmpty()) {
                log.debug("Creating new admin user in insurance company: {}", requestDTO.getAdmin().getEmail());
                UserDTO userDTO = new UserDTO();
                userDTO.setEmail(requestDTO.getAdmin().getEmail());
                userDTO.setUsername(requestDTO.getAdmin().getUsername());
                userDTO.setOrganizationId(savedInsuranceCompany.getId());
                userDTO.setFullName(requestDTO.getAdmin().getFullName());
                userDTO.setPhone(requestDTO.getAdmin().getPhone());
                userDTO.setRoleCode(RoleEnum.IC_ADMIN.name());
                userDTO.setStatus(Status.ACTIVE.toString());

                // Create user in the newly created insurance company
                UUID adminUserId = userService.create(savedInsuranceCompany.getId(), userDTO);

                log.debug("Created new admin user: {} in insurance company: {}",
                        adminUserId, savedInsuranceCompany.getId());
            } else {
                log.debug("No admin user created - admin information not provided");
            }

            long endTime = System.currentTimeMillis();
            log.info("Successfully created insurance company with admin: {} (ID: {}) in {} ms",
                    savedInsuranceCompany.getName(), savedInsuranceCompany.getId(), endTime - startTime);

            return savedInsuranceCompany.getId();
        } catch (ValidationException | NotFoundException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to create insurance company after {} ms: {}", endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.INSURANCE_COMPANY_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void update(UUID id, InsuranceCompanyDTO insuranceCompanyDTO) {
        log.debug("Updating insurance company with ID: {}", id);
        long startTime = System.currentTimeMillis();
        
        try {
            InsuranceCompany existingInsuranceCompany = insuranceCompanyRepository.findByIdAndStatusNot(id, OrganizationStatus.ARCHIVED)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.INSURANCE_COMPANY_NOT_FOUND.getMessageKey())
                    ));

            if (existingInsuranceCompany.getStatus() == OrganizationStatus.ARCHIVED) {
                log.warn("Attempted to update archived insurance company with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.INSURANCE_COMPANY_CANNOT_UPDATE_ARCHIVED.getKey())
                );
            }

            // Validate name duplication (excluding current entity and archived)
            if (insuranceCompanyRepository.findByNameAndIdNot(insuranceCompanyDTO.getName(), id).isPresent()) {
                log.warn("Attempted to update insurance company with duplicate name: {}", insuranceCompanyDTO.getName());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.INSURANCE_COMPANY_NAME_DUPLICATE.getMessageKey())
                );
            }
            log.debug("Updating insurance company entity: {} -> {}",
                    existingInsuranceCompany.getName(), insuranceCompanyDTO.getName());

            insuranceCompanyDTO.setId(id);
            insuranceCompanyMapper.updateEntityFromDTO(insuranceCompanyDTO, existingInsuranceCompany);
            existingInsuranceCompany.setType(OrganizationType.IC);
            insuranceCompanyRepository.save(existingInsuranceCompany);

            long endTime = System.currentTimeMillis();
            log.info("Successfully updated insurance company: {} (ID: {}) in {} ms",
                    existingInsuranceCompany.getName(), existingInsuranceCompany.getId(), endTime - startTime);
        } catch (ValidationException | NotFoundException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to update insurance company with ID {} after {} ms: {}",
                    id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.INSURANCE_COMPANY_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void delete(UUID id) {
        log.debug("Archiving insurance company with ID: {}", id);
        long startTime = System.currentTimeMillis();
        
        try {
            InsuranceCompany insuranceCompany = insuranceCompanyRepository.findByIdAndStatusNot(id, OrganizationStatus.ARCHIVED)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.INSURANCE_COMPANY_NOT_FOUND.getMessageKey())
                    ));

            if (insuranceCompany.getStatus() == OrganizationStatus.ARCHIVED) {
                log.warn("Attempted to archive already archived insurance company with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.INSURANCE_COMPANY_ALREADY_ARCHIVED.getKey())
                );
            }

            log.debug("Archiving insurance company: {} (Current Status: {})",
                    insuranceCompany.getName(), insuranceCompany.getStatus());
            
            insuranceCompany.setStatus(OrganizationStatus.ARCHIVED);
            insuranceCompanyRepository.save(insuranceCompany);

            long endTime = System.currentTimeMillis();
            log.info("Successfully archived insurance company: {} (ID: {}) in {} ms",
                    insuranceCompany.getName(), insuranceCompany.getId(), endTime - startTime);
        } catch (ValidationException | NotFoundException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to archive insurance company with ID {} after {} ms: {}",
                    id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.INSURANCE_COMPANY_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<InsuranceCompanyGeneralInfoDTO> getActiveInsuranceCompanies() {
        log.debug("Fetching list of active insurance companies");
        long startTime = System.currentTimeMillis();

        try {
            List<InsuranceCompany> activeInsuranceCompanies = insuranceCompanyRepository.findByStatus(OrganizationStatus.ACTIVE);

            List<InsuranceCompanyGeneralInfoDTO> result = activeInsuranceCompanies.stream()
                    .map(ic -> InsuranceCompanyGeneralInfoDTO.builder()
                            .id(ic.getId())
                            .name(ic.getName())
                            .build())
                    .toList();

            long endTime = System.currentTimeMillis();
            log.debug("Successfully retrieved {} active insurance companies in {} ms", result.size(), endTime - startTime);

            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to retrieve active insurance companies after {} ms: {}", endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.INSURANCE_COMPANY_OPERATION_FAILED.getKey()), e
            );
        }
    }

    /**
     * Enriches InsuranceCompany DTO with additional data based on user role
     */
    private InsuranceCompanyDTO enrichInsuranceCompanyDTO(InsuranceCompany insuranceCompany) {
        InsuranceCompanyDTO dto = insuranceCompanyMapper.toDTO(insuranceCompany);

        // Set total admin count
        dto.setTotalAdmin(insuranceCompany.getUserOrganizations().size());

        // Check if current user has SUPER_SMAILE_ADMIN role
        if (SecurityUtils.hasRole(RoleEnum.SUPER_SMAILE_ADMIN.name())) {
            List<UserDTO> users = insuranceCompany.getUserOrganizations().stream()
                    .map(userOrg -> {
                        User user = userOrg.getUser();
                        UserDTO userDTO = new UserDTO();

                        // Only set the required fields
                        userDTO.setEmail(user.getEmail());
                        userDTO.setFullName(user.getFullName());
                        userDTO.setPhone(user.getPhone());

                        // Get role code from UserRole
                        String roleCode = userOrg.getUserRoles().stream()
                                .findFirst()
                                .map(userRole -> userRole.getRole().getCode())
                                .orElse(null);
                        userDTO.setRoleCode(roleCode);

                        return userDTO;
                    })
                    .toList();

            dto.setUsers(users);
        } else {
            dto.setUsers(null);
        }

        return dto;
    }

}
