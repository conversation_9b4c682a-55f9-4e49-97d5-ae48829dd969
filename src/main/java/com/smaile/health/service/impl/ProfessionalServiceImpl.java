package com.smaile.health.service.impl;

import com.smaile.health.constants.MessageKey;
import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.ProfessionalDocumentType;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.Professional;
import com.smaile.health.domain.ProfessionalLob;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.ProfessionalMapper;
import com.smaile.health.model.*;
import com.smaile.health.model.request.Filter;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.ProfessionalLobRepository;
import com.smaile.health.repository.ProfessionalRepository;
import com.smaile.health.repository.specification.ProfessionalSpecification;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.OrganizationService;
import com.smaile.health.service.ProfessionalService;
import com.smaile.health.service.UserService;
import com.smaile.health.util.PageResponse;
import com.smaile.health.util.SecurityUtils;
import com.smaile.health.util.UUIDv7;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@AllArgsConstructor
public class ProfessionalServiceImpl implements ProfessionalService {

    private final OrganizationRepository organizationRepository;
    private final ProfessionalRepository professionalRepository;
    private final ProfessionalLobRepository professionalLobRepository;

    private final ProfessionalMapper professionalMapper;
    private final OrganizationService organizationService;
    private final UserService userService;
    private final I18nService i18nService;

    @Override
    @Transactional
    public UUID create(RegisterProfessionalFormDTO registerProfessionalForm) throws IOException {
        // Validate if Professional with same primary license id exists
        if (professionalRepository.existsByPrimaryLicenseId(registerProfessionalForm.getPrimaryLicenseId())) {
            throw new ValidationException(
                    i18nService.getMessage(MessageKey.PROFESSIONAL_LICENSE_ID_EXISTED.getKey(), registerProfessionalForm.getPrimaryLicenseId())
            );
        }

        Professional professional = createProfessional(registerProfessionalForm);
        professionalRepository.save(professional);
        List<ProfessionalLob> professionalLobs = createProfessionalLob(registerProfessionalForm, professional);
        professionalLobRepository.saveAll(professionalLobs);
        professional.setProfessionalLobs(professionalLobs);
        // Create KC user and User as Admin in Professional Org
        UUID userId = userService.createProfessionalUser(professional.getId(),
                UserDTO.builder()
                        .fullName(registerProfessionalForm.getFullName())
                        .email(registerProfessionalForm.getEmail())
                        .build(),
                registerProfessionalForm.getPassword());
        log.info("Create User id {} in Professional Org {} ", userId, professional.getId());
        return professional.getId();
    }

    @Override
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #parentId, 'professionals:*:read')")
    public PageResponse<ProfessionalDTO> query(String search, List<Filter> filters, Pageable pageable) {
        Specification<Professional> spec =
                ProfessionalSpecification
                        .searchProfessional(search)
                        .and(ProfessionalSpecification.withFilter(filters));
        Page<Professional> page = professionalRepository.findAll(spec, pageable);
        Page<ProfessionalDTO> pageResponse = page.map(this::toProfessionalDTO);
        return PageResponse.of(pageResponse);
    }

    @Override
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #parentId, 'professionals:*:read')")
    public ProfessionalDTO detail(UUID professionalId) {
        Professional professional = professionalRepository.findById(professionalId).orElseThrow(() -> new ValidationException(
                i18nService.getMessage(MessageKey.PROFESSIONAL_NOT_FOUND.getKey(), professionalId.toString())
        ));
        return toProfessionalDTO(professional);
    }

    @Override
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #parentId, 'professionals:*:update')")
    @Transactional
    public ProfessionalDTO approve(UUID professionalId) {
        Organization organization = organizationRepository.findById(professionalId).orElseThrow(() ->
            new ValidationException(
                    i18nService.getMessage(MessageKey.PROFESSIONAL_NOT_FOUND.getKey())
            )
        );
        if (OrganizationStatus.DRAFT.equals(organization.getStatus())) {
            int result = organizationRepository.updateStaus(professionalId, OrganizationStatus.ACTIVE, OrganizationStatus.DRAFT);
            log.debug("Applied {} record status change id = {} {} -> {} ", result, professionalId, OrganizationStatus.DRAFT, OrganizationStatus.ACTIVE);
        } else {
            throw new ValidationException(
                    i18nService.getMessage(MessageKey.PROFESSIONAL_INVALID_STATUS.getKey())
            );
        }
        return detail(professionalId);
    }

    @Override
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #parentId, 'professionals:*:update')")
    @Transactional
    public ProfessionalDTO deny(UUID professionalId) {
        Organization organization = organizationRepository.findById(professionalId).orElseThrow(() ->
                new ValidationException(
                        i18nService.getMessage(MessageKey.PROFESSIONAL_NOT_FOUND.getKey())
                )
        );
        if (OrganizationStatus.DRAFT.equals(organization.getStatus())) {
            int result = organizationRepository.updateStaus(professionalId, OrganizationStatus.ARCHIVED, OrganizationStatus.DRAFT);
            log.debug("Applied {} record status change id = {} {} -> {} ", result, professionalId, OrganizationStatus.DRAFT, OrganizationStatus.ACTIVE);
        } else {
            throw new ValidationException(
                    i18nService.getMessage(MessageKey.PROFESSIONAL_INVALID_STATUS.getKey())
            );
        }
        return detail(professionalId);
    }

    @Override
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #parentId, 'professionals:*:read')")
    public ProfessionalSummaryDTO summary() {
        UUID currentOrganizationId = SecurityUtils.getCurrentUserOrganizationId().orElseThrow(() -> new ValidationException(
                i18nService.getMessage(MessageKey.ERROR.getKey())
        ));
        return ProfessionalSummaryDTO.builder()
                .activeCount(professionalRepository.countByStatusAndParentId(OrganizationStatus.ACTIVE, currentOrganizationId))
                .pendingCount(professionalRepository.countByStatusAndParentId(OrganizationStatus.DRAFT, currentOrganizationId))
                .specialtyCount(1)
                .totalCount(professionalRepository.countByParentId(currentOrganizationId))
                .build();
    }

    private Professional createProfessional(RegisterProfessionalFormDTO registerForm) {
        Professional professional = new Professional();
        professional.setId(UUIDv7.generate());
        professional.setParent(organizationService.getSmaileOrganization());

        // Professional Info
        professional.setCountry(registerForm.getCountry());
        professional.setFullProfessionalName(registerForm.getFullProfessionalName());
        professional.setProfessionalSpecialties(registerForm.getProfessionalSpecialties());
        professional.setLicenses(registerForm.getProfessionalLicenses());
        professional.setPrimaryLicenseId(registerForm.getPrimaryLicenseId());
        professional.setPrimaryPracticeMarket(registerForm.getPrimaryPracticeMarket());
        professional.setMarketSegment(registerForm.getMarketSegment());

        // Base Org info
        professional.setContactEmail(registerForm.getEmail());
        professional.setName(registerForm.getFullProfessionalName());
        professional.setContactPhone(registerForm.getPhoneNumber());
        professional.setRegistrationNumber(registerForm.getPrimaryLicenseId());

        professional.setStatus(OrganizationStatus.DRAFT);
        professional.setType(OrganizationType.PROFESSIONAL);
        return professional;
    }

    private List<ProfessionalLob> createProfessionalLob(RegisterProfessionalFormDTO registerProfessionalForm, Professional professional) throws IOException {
        List<ProfessionalLob> lobs = new ArrayList<>();
        if (hasNotEmpty(registerProfessionalForm.getProfessionalLicenseFile())) {
            lobs.add(createProfessionalLob(registerProfessionalForm.getProfessionalLicenseFile(), ProfessionalDocumentType.LICENSE));
        }
        if (hasNotEmpty(registerProfessionalForm.getEducationDiplomaFile())) {
            lobs.add(createProfessionalLob(registerProfessionalForm.getEducationDiplomaFile(), ProfessionalDocumentType.EDUCATION_DIPLOMA));
        }
        if (hasNotEmpty(registerProfessionalForm.getProfessionalCertificateFile())) {
            lobs.add(createProfessionalLob(registerProfessionalForm.getProfessionalCertificateFile(), ProfessionalDocumentType.CERTIFICATE));
        }
        if (hasNotEmpty(registerProfessionalForm.getLiabilityInsuranceFile())) {
            lobs.add(createProfessionalLob(registerProfessionalForm.getLiabilityInsuranceFile(), ProfessionalDocumentType.LIABILITY_INSURANCE));
        }
        if (hasNotEmpty(registerProfessionalForm.getPhotoFile())) {
            lobs.add(createProfessionalLob(registerProfessionalForm.getPhotoFile(), ProfessionalDocumentType.PHOTO));
        }
        lobs.forEach(professionalLob -> professionalLob.setProfessional(professional));
        return lobs;
    }

    private boolean hasNotEmpty(MultipartFile file) {
        return !(
                file == null
                || file.isEmpty()
                || file.getSize() <= 0
                || !StringUtils.hasText(file.getOriginalFilename())
        );
    }

    private ProfessionalLob createProfessionalLob(MultipartFile file, ProfessionalDocumentType documentType) throws IOException {
        ProfessionalLob lob = new ProfessionalLob();
        lob.setId(UUIDv7.generate());
        lob.setFileType(file.getContentType());
        lob.setFileName(file.getOriginalFilename());
        lob.setFileData(Base64.getEncoder().encodeToString(file.getBytes()));
        lob.setDocumentType(documentType);
        return lob;
    }

    private ProfessionalDTO toProfessionalDTO(Professional professional) {
        ProfessionalDTO professionalDTO = professionalMapper.toDTO(professional);
        professionalDTO.setProviders(List.of(
                ProviderDTO.builder()
                        .id(UUID.randomUUID())
                        .name("TODO: This is mock provider")
                        .build(),
                ProviderDTO.builder()
                        .id(UUID.randomUUID())
                        .name("TODO: This is mock provider")
                        .build()
        ));
        return professionalDTO;
    }
}
