package com.smaile.health.service.impl;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.config.CustomAuthentication;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.constants.SmaileConstant;
import com.smaile.health.constants.Status;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.Role;
import com.smaile.health.domain.User;
import com.smaile.health.domain.UserOrganization;
import com.smaile.health.domain.UserRole;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.mapper.UserMapper;
import com.smaile.health.model.AboutMeDTO;
import com.smaile.health.model.UserDTO;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.RoleRepository;
import com.smaile.health.repository.UserOrganizationRepository;
import com.smaile.health.repository.UserRepository;
import com.smaile.health.repository.UserRoleRepository;
import com.smaile.health.security.service.AuthenticationContextService;
import com.smaile.health.service.IdentityProvider;
import com.smaile.health.service.UserService;
import com.smaile.health.util.UUIDv7;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
@Slf4j
public class UserServiceImpl implements UserService {

    private final IdentityProvider identityProvider;
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final OrganizationRepository organizationRepository;
    private final UserOrganizationRepository userOrganizationRepository;
    private final UserRoleRepository userRoleRepository;
    private final UserMapper userMapper;
    private final AuthenticationContextService authenticationContextService;

    @Override
    public CustomAuthentication getAuthenticationByKeycloakId(String keycloakId) {
        try {
            User user = userRepository.findOneByKeycloakId(keycloakId);
            if (user == null || !Objects.equals(keycloakId, user.getKeycloakId())) {
                log.warn("User with kcId = {} was not found", keycloakId);
                CustomAuthentication authentication = new CustomAuthentication();
                authentication.setAuthenticated(false);
                return authentication;
            }

            // Check if user has active organization relationships
            List<UserOrganization> validUserOrganization = user.getUserOrganizations().stream()
                    .filter(uo -> Objects.equals(uo.getStatus(), SmaileConstant.ACTIVE))
                    .toList();
            if (validUserOrganization.isEmpty()) {
                throw new SmaileRuntimeException("User has no active user-organization relation");
            }

            // Build complete authentication context with organization-specific permissions
            CustomAuthentication authentication = authenticationContextService.buildAuthenticationContext(user);

            log.debug("User keycloak id = {}, Authentication context built with {} organization contexts",
                     keycloakId, authentication.getOrganizationPermissionContexts().size());

            return authentication;

        } catch (Exception e) {
            log.error("Failed to build authentication for user with keycloak id: {}", keycloakId, e);
            CustomAuthentication authentication = new CustomAuthentication();
            authentication.setAuthenticated(false);
            return authentication;
        }
    }

    @Override
    public AboutMeDTO getAboutMe() {
        CustomAuthentication authentication = (CustomAuthentication) SecurityContextHolder.getContext().getAuthentication();
        User user = authentication.getActor();

        return AboutMeDTO.builder()
                .userId(user.getId())
                .fullName(user.getFullName())
                .email(user.getEmail())
                .phone(user.getPhone())
                .status(user.getStatus())
                .organizationId(Optional.ofNullable(user.getOrganization()).map(Organization::getId).orElse(null))
                .organizationType(Optional.ofNullable(user.getOrganization()).map(Organization::getType).orElse(null))
                .role(Optional.ofNullable(user.getRole()).map(Role::getCode).orElse(null))
                .build();
    }

    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #scopedOrgId, 'users:*:read')")
    @Override
    public Page<UserDTO> findActiveUserByOrgId(UUID scopedOrgId, Pageable pageable) {
        final Page<User> users = userRepository.findByOrgIdAndStatus(scopedOrgId, SmaileConstant.ACTIVE, pageable);
        return users.map(userMapper::toDTO);
    }

    public UserDTO get(final UUID id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new SmaileRuntimeException("User with id = %s not found".formatted(id)));
        return userMapper.toDTO(user);
    }


    @Override
    @LogExecution
    public boolean isAvailableToCreate(UserDTO userDto) {
        if (userRepository.findOneByEmail(userDto.getEmail()).isPresent()) {
            return false;
        }
        if (identityProvider.findByEmailAndUsername(userDto.getEmail(), userDto.getUsername()).isPresent()) {
            return false;
        }
        return true;
    }

    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #organizationId, 'users:*:read')")
    @LogExecution
    @Override
    public Page<UserDTO> queryUserByCriteria(UUID organizationId, String partialNameOrEmail, RoleEnum role, Status status, Pageable pageable) {
        Page<User> userPage = userRepository.findAll((root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (organizationId != null) {
                Join<User, Organization> organization = root.join("organization", JoinType.LEFT);
                predicates.add(cb.equal(organization.get("id"), organizationId));
            }

            if (StringUtils.isNotBlank(partialNameOrEmail)) {
                String pattern = "%" + partialNameOrEmail.toLowerCase() + "%";
                predicates.add(cb.or(
                        cb.like(cb.lower(root.get("email")), pattern),
                        cb.like(cb.lower(root.get("fullName")), pattern)
                ));
            }
            if (status != null) {
                predicates.add(cb.equal(root.get("status"), status.name()));
            }
            if (role != null) {
                predicates.add(cb.equal(root.get("role").get("code"), role.name()));
            }
            return predicates.stream().reduce(cb::and).orElseGet(cb::conjunction);
        }, pageable);
        return userPage.map(userMapper::toDTO);
    }

    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #scopedOrgId, 'users:*:create')")
    @LogExecution
    @Transactional
    public UUID create(UUID scopedOrgId, final UserDTO userDto) {
        Organization organization = organizationRepository.findById(scopedOrgId)
                .orElseThrow(() -> new SmaileRuntimeException("No organization found for id [%s]".formatted(userDto.getOrganizationId())));
        userRepository.findOneByEmail(userDto.getEmail())
                .ifPresent(u -> {
                    throw new SmaileRuntimeException("User with email [%s] existed".formatted(u.getEmail()));
                });
        Role role = roleRepository.findOneByCode(userDto.getRoleCode())
                .orElseThrow(() -> new SmaileRuntimeException("No role found for code %s".formatted(userDto.getRoleCode())));

        // Temporary disable this validation for quick development
//        if (StringUtils.endsWithIgnoreCase(userDto.getRoleCode(), "ADMIN") && userDto.getPhone() == null) {
//            throw new SmaileValidationException(Map.of("User", "with empty phone cannot be initialized as %s".formatted(userDto.getRoleCode())));
//        }

        String kcUserId = identityProvider.createUser(userDto);

        User user = User.builder()
                .id(UUIDv7.generate())
                .keycloakId(kcUserId)
                .email(userDto.getEmail())
                .fullName(userDto.getFullName())
                .phone(userDto.getPhone())
                .status(userDto.getStatus())
                .organization(organization)
                .role(role)
                .build();
        log.debug("Creating db user: {}", user);
        userRepository.save(user);
        log.debug("Smaile user created, smaile user id = {}", user.getId());

        UserOrganization userOrg = UserOrganization.builder()
                .id(UUIDv7.generate())
                .user(user)
                .organization(organization)
                .status(SmaileConstant.ACTIVE)
                .build();
        userOrganizationRepository.save(userOrg);

        UserRole userRole = UserRole.builder()
                .id(UUIDv7.generate())
                .userOrganization(userOrg)
                .role(role)
                .startTime(SmaileConstant.DEFAULT_START_TIME)
                .endTime(SmaileConstant.DEFAULT_END_TIME)
                .status(SmaileConstant.ACTIVE)
                .build();
        userRoleRepository.save(userRole);

        return user.getId();
    }

    @Override
    public UUID createProfessionalUser(UUID orgId, UserDTO userDto, String password) {
        Organization organization = organizationRepository.findById(orgId)
                .orElseThrow(() -> new SmaileRuntimeException("No organization found for id [%s]".formatted(orgId)));
        if (!OrganizationType.PROFESSIONAL.equals(organization.getType())) {
            throw new SmaileRuntimeException("Organization with id [%s] is not a professional organization".formatted(orgId));
        }
        userRepository.findOneByEmail(userDto.getEmail())
                .ifPresent(u -> {
                    throw new SmaileRuntimeException("User with email [%s] existed".formatted(u.getEmail()));
                });
        Role role = roleRepository.findOneByCode(RoleEnum.PROFESSIONAL.name()).orElseThrow(() -> new SmaileRuntimeException("No role found for code %s".formatted(RoleEnum.PROFESSIONAL.name())));
        String kcUserId = identityProvider.createUser(userDto.getFullName(), userDto.getEmail(), password);
        log.info("Created KC user id {} ", kcUserId);
        User user = User.builder()
                .id(UUIDv7.generate())
                .keycloakId(kcUserId)
                .email(userDto.getEmail())
                .fullName(userDto.getFullName())
                .status(userDto.getStatus())
                .organization(organization)
                .role(role)
                .build();
        userRepository.save(user);

        UserOrganization userOrg = UserOrganization.builder()
                .id(UUIDv7.generate())
                .user(user)
                .organization(organization)
                .status(SmaileConstant.ACTIVE)
                .build();
        userOrganizationRepository.save(userOrg);

        UserRole userRole = UserRole.builder()
                .id(UUIDv7.generate())
                .userOrganization(userOrg)
                .role(role)
                .startTime(SmaileConstant.DEFAULT_START_TIME)
                .endTime(SmaileConstant.DEFAULT_END_TIME)
                .status(SmaileConstant.ACTIVE)
                .build();
        userRoleRepository.save(userRole);
        return user.getId();
    }

    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #scopedOrgId, 'users:*:update')")
    public void update(final UUID scopedOrgId, final UUID userId, final UserDTO userDto) {
        if (!Objects.equals(userId, userDto.getId())) {
            throw new SmaileRuntimeException("User id in payload [%s] not match [%s]".formatted(userDto.getId(), userId));
        }

        User existingUser = userRepository.findById(userId)
                .filter(user -> user.getUserOrganizations().stream()
                        .filter(uo -> Objects.equals(uo.getStatus(), SmaileConstant.ACTIVE))
                        .anyMatch(uo -> Objects.equals(uo.getOrganization().getId(), scopedOrgId))
                )
                .orElseThrow(() -> new SmaileRuntimeException("User with id = %s not found for org %s".formatted(userId, scopedOrgId)));

        existingUser.setFullName(userDto.getFullName());
        existingUser.setStatus(userDto.getStatus());
        existingUser.setPhone(userDto.getPhone());

        userRepository.save(existingUser);
    }

}
