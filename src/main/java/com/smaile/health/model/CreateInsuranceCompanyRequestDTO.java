package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Schema(description = "Create Insurance Company Request Data Transfer Object")
public class CreateInsuranceCompanyRequestDTO {

    @Schema(
            description = "Name of the insurance company",
            example = "ABC Insurance Co.",
            required = true,
            maxLength = 255
    )
    @NotBlank(message = "Name is required")
    @Size(max = 255, message = "Name must not exceed 255 characters")
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "Type of insurance business (e.g. IC, TPA)",
            example = "IC",
            maxLength = 100
    )
    @Size(max = 100, message = "Type must not exceed 100 characters")
    @JsonProperty("type")
    private String type;

    @Schema(
            description = "Internal code for the insurance company",
            example = "ABC001",
            maxLength = 100
    )
    @Size(max = 100, message = "Code must not exceed 100 characters")
    @JsonProperty("code")
    private String code;

    @Schema(
            description = "Official registration number issued by regulatory authorities",
            example = "REG123456",
            maxLength = 100
    )
    @Size(max = 100, message = "Registration number must not exceed 100 characters")
    @JsonProperty("registration_number")
    private String registrationNumber;

    @Schema(
            description = "Contact phone number for the insurance company",
            example = "******-0123",
            maxLength = 20
    )
    @Size(max = 20, message = "Contact phone must not exceed 20 characters")
    @JsonProperty("contact_phone")
    private String contactPhone;

    @Schema(
            description = "Contact email address for the insurance company",
            example = "<EMAIL>",
            maxLength = 255
    )
    @Size(max = 255, message = "Contact email must not exceed 255 characters")
    @JsonProperty("contact_email")
    private String contactEmail;

    @Schema(
            description = "Physical address of the insurance company",
            example = "123 Main St, New York, NY 10001",
            maxLength = 500
    )
    @Size(max = 500, message = "Address must not exceed 500 characters")
    @JsonProperty("address")
    private String address;

    @Schema(
            description = "Market/region where the insurance company operates",
            example = "US",
            required = true,
            maxLength = 100
    )
    @NotBlank(message = "Market is required")
    @Size(max = 100, message = "Market must not exceed 100 characters")
    @JsonProperty("market")
    private String market;

    @Schema(
            description = "Country where the insurance company is registered",
            example = "United States",
            required = true,
            maxLength = 100
    )
    @NotBlank(message = "Country is required")
    @Size(max = 100, message = "Country must not exceed 100 characters")
    @JsonProperty("country")
    private String country;

    @Schema(
            description = "Admin user information for this insurance company (optional)",
            required = false
    )
    @JsonProperty("admin")
    private AdminDTO admin;
}
