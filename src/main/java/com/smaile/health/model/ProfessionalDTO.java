package com.smaile.health.model;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
public class ProfessionalDTO {

    private String id;
    private String fullProfessionalName;
    private String primaryLicenseId;
    private String primaryPracticeMarket;
    private String country;
    private String marketSegment;
    private String name;
    private String code;
    private OrganizationType type;
    private String registrationNumber;
    private OrganizationStatus status;
    private String contactPhone;
    private String contactEmail;
    private String address;
    private List<ProfessionalLicenseDTO> licenses;
    private List<String> professionalSpecialties;

    private List<ProviderDTO> providers;

}
